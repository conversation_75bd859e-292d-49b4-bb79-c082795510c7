
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'dart:async';
import '../models/report_models.dart';
import '../services/reports_repository.dart';
import '../services/reports_analytics_service.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Milk Records/models/milk_record_isar.dart';
import '../../Health/models/health_record_isar.dart';
import '../../Breeding/models/breeding_record_isar.dart';
import '../../Weight/models/weight_record_isar.dart';
import '../../Transactions/models/transaction_isar.dart';
import '../../Events/models/event_isar.dart';

/// Controller state enum
enum ControllerState { initial, loading, loaded, error, empty }

/// Reports Controller
///
/// Manages state for the unified reports system following established patterns.
/// Implements dual-stream architecture with filtered/unfiltered data separation.
/// Analytics are ALWAYS calculated on unfiltered data for accuracy.
///
/// Follows the cattle/weight controller pattern with reactive streams.
class ReportsController extends ChangeNotifier {
  // Use lazy getters to avoid accessing GetIt services in constructor
  ReportsRepository get _reportsRepository => GetIt.instance<ReportsRepository>();

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;

  // Stream subscriptions for real-time updates - Separated for filtered/unfiltered data
  StreamSubscription<List<CattleIsar>>? _unfilteredCattleSubscription;
  StreamSubscription<List<MilkRecordIsar>>? _unfilteredMilkSubscription;
  StreamSubscription<List<HealthRecordIsar>>? _unfilteredHealthSubscription;
  StreamSubscription<List<BreedingRecordIsar>>? _unfilteredBreedingSubscription;
  StreamSubscription<List<WeightRecordIsar>>? _unfilteredWeightSubscription;
  StreamSubscription<List<TransactionIsar>>? _unfilteredTransactionSubscription;
  StreamSubscription<List<EventIsar>>? _unfilteredEventSubscription;

  // Data - Critical separation: filtered data for UI, unfiltered for analytics
  List<CattleIsar> _unfilteredCattle = []; // Complete dataset for analytics calculations
  List<MilkRecordIsar> _unfilteredMilkRecords = [];
  List<HealthRecordIsar> _unfilteredHealthRecords = [];
  List<BreedingRecordIsar> _unfilteredBreedingRecords = [];
  List<WeightRecordIsar> _unfilteredWeightRecords = [];
  List<TransactionIsar> _unfilteredTransactions = [];
  List<EventIsar> _unfilteredEvents = [];

  // Current report data and settings
  ReportData? _currentReportData;
  ReportType _selectedReportType = ReportType.dashboard;
  FilterState _currentFilter = const FilterState();
  bool _hasActiveFilters = false; // Track if filters are currently applied

  // Analytics result from the dedicated service - ALWAYS calculated on unfiltered data
  ReportsAnalyticsResult _analyticsResult = ReportsAnalyticsResult.empty;

  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;
  ReportData? get currentReportData => _currentReportData;
  ReportType get selectedReportType => _selectedReportType;
  FilterState get currentFilter => _currentFilter;
  bool get hasActiveFilters => _hasActiveFilters;
  ReportsAnalyticsResult get analytics => _analyticsResult;

  // Additional getters for compatibility
  bool get isLoading => _state == ControllerState.loading;
  ReportData? get dashboardData => _currentReportData;

  // Constructor
  ReportsController() {
    _initializeStreamListeners();
  }

  // Initialize and load data
  Future<void> loadData() async {
    try {
      _setState(ControllerState.loading);
      // Data comes from Isar watch() streams automatically
      // State management is handled by _handleStreamUpdate
    } catch (e, stackTrace) {
      debugPrint('Error loading reports data: $e\n$stackTrace');
      _setError('Failed to load reports data: ${e.toString()}');
    }
  }

  /// Initialize stream listeners for real-time updates using Isar's native watch
  /// Critical: Separate streams for unfiltered (analytics) data
  void _initializeStreamListeners() {
    // Primary streams: Unfiltered data for analytics calculations
    // These streams NEVER change and always provide the complete dataset
    _unfilteredCattleSubscription = _reportsRepository.watchAllCattle()
        .listen((unfilteredList) {
      _unfilteredCattle = unfilteredList;
      _updateAnalytics();
      _generateCurrentReport();
    });

    _unfilteredMilkSubscription = _reportsRepository.watchAllMilkRecords()
        .listen((unfilteredList) {
      _unfilteredMilkRecords = unfilteredList;
      _updateAnalytics();
      _generateCurrentReport();
    });

    _unfilteredHealthSubscription = _reportsRepository.watchAllHealthRecords()
        .listen((unfilteredList) {
      _unfilteredHealthRecords = unfilteredList;
      _updateAnalytics();
      _generateCurrentReport();
    });

    _unfilteredBreedingSubscription = _reportsRepository.watchAllBreedingRecords()
        .listen((unfilteredList) {
      _unfilteredBreedingRecords = unfilteredList;
      _updateAnalytics();
      _generateCurrentReport();
    });

    _unfilteredWeightSubscription = _reportsRepository.watchAllWeightRecords()
        .listen((unfilteredList) {
      _unfilteredWeightRecords = unfilteredList;
      _updateAnalytics();
      _generateCurrentReport();
    });

    _unfilteredTransactionSubscription = _reportsRepository.watchAllTransactions()
        .listen((unfilteredList) {
      _unfilteredTransactions = unfilteredList;
      _updateAnalytics();
      _generateCurrentReport();
    });

    _unfilteredEventSubscription = _reportsRepository.watchAllEvents()
        .listen((unfilteredList) {
      _unfilteredEvents = unfilteredList;
      _updateAnalytics();
      _generateCurrentReport();
    });
  }

  /// Update analytics - ALWAYS calculated on unfiltered data
  void _updateAnalytics() {
    _analyticsResult = ReportsAnalyticsService.calculateDashboard(
      cattle: _unfilteredCattle,
      milkRecords: _unfilteredMilkRecords,
      healthRecords: _unfilteredHealthRecords,
      breedingRecords: _unfilteredBreedingRecords,
      weightRecords: _unfilteredWeightRecords,
      transactions: _unfilteredTransactions,
      events: _unfilteredEvents,
      filter: _currentFilter,
    );
  }

  /// Generate current report based on selected type and filters
  void _generateCurrentReport() {
    try {
      switch (_selectedReportType) {
        case ReportType.dashboard:
          _generateDashboardReport();
          break;
        case ReportType.cattle:
          _generateCattleReport();
          break;
        case ReportType.milk:
          _generateMilkReport();
          break;
        case ReportType.health:
          _generateHealthReport();
          break;
        case ReportType.breeding:
          _generateBreedingReport();
          break;
        case ReportType.weight:
          _generateWeightReport();
          break;
        case ReportType.financial:
          _generateFinancialReport();
          break;
      }
      _setState(ControllerState.loaded);
    } catch (e) {
      _setError('Failed to generate report: ${e.toString()}');
    }
  }

  void _generateDashboardReport() {
    // Create dashboard report with key metrics from all modules
    final metrics = <String, ReportMetric>{};
    final chartData = <ChartPoint>[];
    final insights = <String>[];

    // Add key metrics from analytics
    metrics['total_cattle'] = ReportMetric.kpi(
      title: 'Total Cattle',
      value: _analyticsResult.totalCattle.toString(),
      icon: Icons.pets,
      color: Colors.brown,
    );

    metrics['active_cattle'] = ReportMetric.kpi(
      title: 'Active Cattle',
      value: _analyticsResult.activeCattle.toString(),
      icon: Icons.check_circle,
      color: Colors.green,
    );

    metrics['milk_production'] = ReportMetric.kpi(
      title: 'Total Milk Production',
      value: '${_analyticsResult.totalMilkProduction.toStringAsFixed(1)}L',
      icon: Icons.local_drink,
      color: Colors.lightBlue,
    );

    metrics['net_profit'] = ReportMetric.kpi(
      title: 'Net Profit',
      value: '\$${_analyticsResult.netProfit.toStringAsFixed(2)}',
      icon: Icons.account_balance,
      color: _analyticsResult.netProfit >= 0 ? Colors.green : Colors.red,
    );

    _currentReportData = ReportData(
      title: 'Farm Dashboard',
      subtitle: 'Overview of farm operations',
      generated: DateTime.now(),
      startDate: _currentFilter.startDate,
      endDate: _currentFilter.endDate,
      metrics: metrics,
      chartData: chartData,
      tableData: [],
      insights: insights,
      type: ReportType.dashboard,
    );
  }

  void _generateCattleReport() {
    _currentReportData = ReportsAnalyticsService.calculateCattleReport(
      _unfilteredCattle, _currentFilter);
  }

  void _generateMilkReport() {
    _currentReportData = ReportsAnalyticsService.calculateMilkReport(
      _unfilteredMilkRecords, _currentFilter);
  }

  void _generateHealthReport() {
    // Placeholder - implement when needed
    _currentReportData = ReportData.empty(ReportType.health);
  }

  void _generateBreedingReport() {
    // Placeholder - implement when needed
    _currentReportData = ReportData.empty(ReportType.breeding);
  }

  void _generateWeightReport() {
    // Placeholder - implement when needed
    _currentReportData = ReportData.empty(ReportType.weight);
  }

  void _generateFinancialReport() {
    _currentReportData = ReportsAnalyticsService.calculateFinancialReport(
      _unfilteredTransactions, _currentFilter);
  }

  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String error) {
    _state = ControllerState.error;
    _errorMessage = error;
    notifyListeners();
  }

  /// Apply filters to reports - triggers database-level filtering
  Future<void> applyFilters(FilterState filter) async {
    try {
      _setState(ControllerState.loading);
      _currentFilter = filter;
      _hasActiveFilters = _hasFilters(filter);

      // Regenerate current report with new filters
      _generateCurrentReport();
    } catch (e) {
      _setError('Failed to apply filters: ${e.toString()}');
    }
  }

  /// Clear all filters
  Future<void> clearFilters() async {
    await applyFilters(const FilterState());
  }

  /// Change report type
  Future<void> changeReportType(ReportType type) async {
    if (_selectedReportType != type) {
      _selectedReportType = type;
      _generateCurrentReport();
      notifyListeners();
    }
  }

  /// Check if filter has active filters
  bool _hasFilters(FilterState filter) {
    return filter.startDate != null ||
           filter.endDate != null ||
           (filter.cattleIds?.isNotEmpty ?? false);
  }

  /// Refresh all data
  Future<void> refresh() async {
    try {
      // Force analytics recalculation on unfiltered data
      _updateAnalytics();
      _generateCurrentReport();
      notifyListeners();
    } catch (e, stackTrace) {
      debugPrint('Error refreshing reports data: $e\n$stackTrace');
      throw Exception('Failed to refresh reports data: ${e.toString()}');
    }
  }

  /// Initialize the controller
  Future<void> initialize() async {
    await loadData();
  }

  /// Refresh data
  Future<void> refreshData() async {
    try {
      _setState(ControllerState.loading);
      // Trigger data refresh through stream listeners
      _updateAnalytics();
      _setState(ControllerState.loaded);
    } catch (e, stackTrace) {
      debugPrint('Error refreshing reports data: $e\n$stackTrace');
      _setError('Failed to refresh reports data: ${e.toString()}');
    }
  }

  /// Refresh dashboard data
  Future<void> refreshDashboard() async {
    await refreshData();
  }

  /// Select a report type
  Future<void> selectReportType(ReportType reportType) async {
    _selectedReportType = reportType;
    notifyListeners();
    await refreshData();
  }

  /// Update date range
  Future<void> updateDateRange(DateTime startDate, DateTime endDate) async {
    _currentFilter = _currentFilter.copyWith(
      startDate: startDate,
      endDate: endDate,
    );
    _hasActiveFilters = true;
    notifyListeners();
    await refreshData();
  }

  /// Export dashboard as PDF
  Future<void> exportDashboardPDF() async {
    try {
      // Implementation would go here
      debugPrint('Exporting dashboard as PDF...');
    } catch (e) {
      debugPrint('Error exporting PDF: $e');
      rethrow;
    }
  }

  /// Export dashboard as Excel
  Future<void> exportDashboardExcel() async {
    try {
      // Implementation would go here
      debugPrint('Exporting dashboard as Excel...');
    } catch (e) {
      debugPrint('Error exporting Excel: $e');
      rethrow;
    }
  }

  /// Batch export all reports
  Future<void> batchExportAll() async {
    try {
      // Implementation would go here
      debugPrint('Batch exporting all reports...');
    } catch (e) {
      debugPrint('Error batch exporting: $e');
      rethrow;
    }
  }

  @override
  void dispose() {
    // Cancel all stream subscriptions to prevent memory leaks
    _unfilteredCattleSubscription?.cancel();
    _unfilteredMilkSubscription?.cancel();
    _unfilteredHealthSubscription?.cancel();
    _unfilteredBreedingSubscription?.cancel();
    _unfilteredWeightSubscription?.cancel();
    _unfilteredTransactionSubscription?.cancel();
    _unfilteredEventSubscription?.cancel();
    super.dispose();
  }
}