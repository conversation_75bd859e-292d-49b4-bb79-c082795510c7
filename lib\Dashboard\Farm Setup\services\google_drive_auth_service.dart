import 'package:google_sign_in/google_sign_in.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:http/http.dart' as http;
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import '../../User Account/services/auth_service.dart';

class GoogleDriveAuthService {
  static final GoogleDriveAuthService _instance = GoogleDriveAuthService._internal();

  // Use a separate GoogleSignIn instance specifically for Drive access
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: [
      drive.DriveApi.driveFileScope,
    ],
  );

  final _storage = const FlutterSecureStorage();
  final _logger = Logger('GoogleDriveAuthService');
  GoogleSignInAccount? _currentUser;
  AuthClient? _authClient;

  // Get the main AuthService to check if user is already authenticated
  AuthService get _authService => GetIt.instance<AuthService>();

  factory GoogleDriveAuthService() {
    return _instance;
  }

  GoogleDriveAuthService._internal();

  GoogleSignInAccount? get currentUser => _currentUser;
  AuthClient? get authClient => _authClient;

  Future<bool> initialize() async {
    try {
      _logger.info('Initializing Google Drive auth service...');

      // Check if user is already authenticated with the main auth service
      if (_authService.isAuthenticated && _authService.currentUser?.isGoogleUser == true) {
        _logger.info('User authenticated with main auth service, attempting silent sign-in...');
      }

      // Try to sign in silently with cached credentials
      _currentUser = await _googleSignIn.signInSilently();
      if (_currentUser != null) {
        _logger.info('Silent sign in successful for: ${_currentUser!.email}');
        await _getAuthClient();
        return true;
      } else {
        _logger.info('No cached Google credentials found');
        return false;
      }
    } catch (e) {
      _logger.warning('Error initializing auth service: $e');
      return false;
    }
  }

  Future<bool> signIn() async {
    try {
      _logger.info('Attempting Google Drive sign in...');

      // First check if user is already authenticated with the main auth service
      if (_authService.isAuthenticated && _authService.currentUser?.isGoogleUser == true) {
        _logger.info('User already authenticated with Google, attempting silent sign-in...');
        _currentUser = await _googleSignIn.signInSilently();

        if (_currentUser != null) {
          _logger.info('Silent Google sign in successful for: ${_currentUser!.email}');
          await _getAuthClient();
          return true;
        }
      }

      // If silent sign-in failed, try regular sign-in
      _logger.info('Attempting interactive Google sign-in...');
      _currentUser = await _googleSignIn.signIn();
      if (_currentUser != null) {
        _logger.info('Google sign in successful for: ${_currentUser!.email}');
        await _getAuthClient();
        return true;
      } else {
        _logger.warning('Google sign in cancelled by user');
        return false;
      }
    } catch (e) {
      _logger.warning('Error signing in: $e');
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      await _storage.delete(key: 'auth_token');
      _currentUser = null;
      _authClient?.close();
      _authClient = null;
    } catch (e) {
      _logger.warning('Error during sign out: $e');
      // Ensure resources are cleaned up even if there's an error
      _currentUser = null;
      _authClient?.close();
      _authClient = null;
    }
  }

  Future<void> _getAuthClient() async {
    try {
      _logger.info('Getting authentication client for Google Drive...');
      final auth = await _currentUser!.authentication;
      final accessToken = auth.accessToken;

      if (accessToken != null) {
        _logger.info('Access token obtained, creating authenticated client...');

        // Store tokens securely
        await _storage.write(key: 'accessToken', value: accessToken);

        // Create auth client with UTC expiry time
        final client = http.Client();
        final expiryTime = DateTime.now().toUtc().add(const Duration(hours: 1));

        _logger.info('Creating AccessToken with expiry: ${expiryTime.toIso8601String()}');

        _authClient = authenticatedClient(
          client,
          AccessCredentials(
            AccessToken(
              'Bearer',
              accessToken,
              expiryTime,
            ),
            null,
            [drive.DriveApi.driveFileScope],
          ),
        );

        _logger.info('Google Drive auth client created successfully');
      } else {
        _logger.warning('No access token available from Google authentication');
      }
    } catch (e) {
      _logger.severe('Error getting auth client: $e');
      rethrow;
    }
  }

  bool isSignedIn() {
    return _currentUser != null && _authClient != null;
  }
}