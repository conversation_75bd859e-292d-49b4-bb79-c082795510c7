import 'dart:io';
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import '../controllers/events_controller.dart';
// Using streamlined architecture - export functionality inline
import '../../widgets/index.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_tabs.dart';
import '../../../shared/models/info_card_data.dart';

class EventAnalyticsTab extends StatefulWidget {
  final EventsController controller;
  const EventAnalyticsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);
  @override
  State<EventAnalyticsTab> createState() => _EventAnalyticsTabState();
}
class _EventAnalyticsTabState extends State<EventAnalyticsTab> {
  int _selectedChartIndex = 0; // 0: Status, 1: Category, 2: Priority
  // Use global constants instead of local ones
  static const _fallbackColor = AppColors.fallback;
  // Common chart configuration
  static const _chartRadius = 80.0;
  static const _chartCenterSpace = 50.0;
  static const _chartSectionsSpace = 2.0;
  static const _chartHeight = 240.0;
  // Common text styles
  static const _chartTitleStyle = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.bold,
    color: Colors.white,
  );
  @override
  void initState() {
    super.initState();
    // Data loading is now handled by the controller
  }
  // High-level abstraction for grid sections using universal components
  Widget _buildGridSection({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color headerColor,
    required List<Map<String, dynamic>> cardData,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 1. Build the section header using universal component
        UniversalFormField.sectionHeader(
          title: title,
          icon: icon,
          color: headerColor,
          subtitle: subtitle,
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium),
        // 2. Use ResponsiveGrid.cards for consistent responsive behavior
        ResponsiveGrid.cards(
          children: cardData.map((data) {
            return UniversalInfoCard(
              title: data['title'] as String,
              value: data['value'] as String,
              subtitle: data['subtitle'] as String?,
              icon: data['icon'] as IconData,
              color: data['color'] as Color,
              badge: data['badge'] as String?,
              insight: data['insight'] as String?,
            );
          }).toList(),
        ),
      ],
    );
  }
  // Universal pie chart builder - pure UI component without business logic
  Widget _buildUniversalPieChart(Map<String, int> data, Map<String, Color> colors, {String? emptyMessage}) {
    if (data.isEmpty || widget.controller.totalEvents == 0) {
      return Center(child: Text(emptyMessage ?? 'No data available'));
    }
    // Ensure we have valid data before rendering
    final validEntries = data.entries.where((entry) => entry.value > 0).toList();
    if (validEntries.isEmpty) {
      return Center(child: Text(emptyMessage ?? 'No data available'));
    }
    final sections = validEntries.map((entry) {
      final percentage = (entry.value / widget.controller.totalEvents) * 100;
      final sectionColor = colors[entry.key] ?? _fallbackColor;
      return PieChartSectionData(
        color: sectionColor,
        value: entry.value.toDouble(),
        title: '${percentage.toStringAsFixed(1)}%',
        radius: _chartRadius,
        titleStyle: _chartTitleStyle,
      );
    }).toList();
    // Add a small delay to ensure smooth rendering
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: PieChart(
        PieChartData(
          sections: sections,
          centerSpaceRadius: _chartCenterSpace,
          sectionsSpace: _chartSectionsSpace,
          borderData: FlBorderData(show: false),
        ),
      ),
    );
  }
  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        // Check if we have data to display
        if (widget.controller.totalEvents == 0) {
          return UniversalTabEmptyState.forTab(
            title: 'No Event Data',
            message: 'Add events to your farm to view comprehensive analytics and insights.',
            tabColor: AppColors.eventsHeader,
            tabIndex: 2, // Analytics tab
            action: TabEmptyStateActions.addFirstRecord(
              onPressed: () {
                // Navigate to add event screen
                Navigator.of(context).pushNamed('/events/add');
              },
              tabColor: AppColors.eventsHeader,
            ),
          );
        }
        // Define sections for clean, declarative layout
        final sections = [
          _buildEnhancedKPIDashboard(context),
          _buildEventStatusAnalytics(context),
          _buildPerformanceMetrics(context),
          _buildCattleSpecificAnalytics(context),
          _buildCostAnalysis(context),
          _buildAutomationInsights(context),
          _buildExportSection(context),
        ];
        // Use RefreshIndicator with SingleChildScrollView for pull-to-refresh functionality
        return RefreshIndicator(
          onRefresh: () async {
            await widget.controller.refresh();
          },
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(kPaddingMedium), // Use global constant
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                for (int i = 0; i < sections.length; i++) ...[
                  sections[i],
                  if (i < sections.length - 1) const SizedBox(height: kSpacingLarge), // Use global constant
                ],
              ],
            ),
          ),
        );
      },
    );
  }
  Widget _buildEnhancedKPIDashboard(BuildContext context) {
    final kpiColors = _getKPIColors();
    final kpiCards = _buildKPICards(kpiColors);
    return _buildGridSection(
      title: 'Key Performance Indicators',
      subtitle: 'Essential metrics for your event management',
      icon: Icons.dashboard_outlined,
      headerColor: AppColors.eventsHeader,
      cardData: kpiCards.map((card) => card.toMap()..['badge'] = null).toList(),
    );
  }
  /// Build KPI cards using InfoCardData for better type safety
  List<InfoCardData> _buildKPICards(List<Color> kpiColors) {
    return [
      InfoCardData(
        title: 'Total Events',
        value: widget.controller.totalEvents.toString(),
        subtitle: 'all events',
        icon: Icons.event,
        color: kpiColors[0],
        insight: 'Total event records',
      ),
      InfoCardData(
        title: 'Completed',
        value: widget.controller.completedEvents.toString(),
        subtitle: 'finished events',
        icon: Icons.check_circle,
        color: kpiColors[1],
        insight: 'Successfully completed',
      ),
      InfoCardData(
        title: 'Scheduled',
        value: widget.controller.scheduledEvents.toString(),
        subtitle: 'upcoming events',
        icon: Icons.schedule,
        color: kpiColors[2],
        insight: 'Planned activities',
      ),
      InfoCardData(
        title: 'Overdue',
        value: widget.controller.overdueEvents.toString(),
        subtitle: 'need attention',
        icon: Icons.warning,
        color: kpiColors[3],
        insight: 'Requires immediate action',
      ),
      InfoCardData(
        title: 'Completion Rate',
        value: '${widget.controller.completionRate.toStringAsFixed(1)}%',
        subtitle: 'success rate',
        icon: Icons.trending_up,
        color: kpiColors[4],
        insight: 'Event completion efficiency',
      ),
      InfoCardData(
        title: 'This Month',
        value: widget.controller.eventsThisMonth.toString(),
        subtitle: 'monthly events',
        icon: Icons.calendar_month,
        color: kpiColors[5],
        insight: 'Current month activity',
      ),
    ];
  }
  Widget _buildEventStatusAnalytics(BuildContext context) {
    final statusData = widget.controller.eventsByStatus;
    final categoryData = widget.controller.eventsByCategory;
    final priorityData = widget.controller.eventsByPriority;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UniversalFormField.sectionHeader(
          title: 'Event Distribution Analytics',
          icon: Icons.pie_chart_outline,
          color: AppColors.eventsHeader,
          subtitle: 'Detailed breakdown of events by status, category, and priority',
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium),
        // Toggle buttons for chart selection
        _buildChartToggleButtons(context),
        const SizedBox(height: kSpacingMedium),
        // Single chart display based on selection
        _buildSelectedChart(context, statusData, categoryData, priorityData),
      ],
    );
  }
  Widget _buildChartToggleButtons(BuildContext context) {
    final chartOptions = [
      {'title': 'Status', 'icon': Icons.assignment},
      {'title': 'Category', 'icon': Icons.category},
      {'title': 'Priority', 'icon': Icons.priority_high},
    ];
    // Use colors from AppColors instead of hardcoded values
    final toggleColors = [
      AppColors.eventsKpiColors[0], // Purple
      AppColors.eventsKpiColors[1], // Green
      AppColors.eventsKpiColors[2], // Red
    ];
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: chartOptions.asMap().entries.map((entry) {
        final index = entry.key;
        final option = entry.value;
        final isSelected = _selectedChartIndex == index;
        return ChoiceChip(
          label: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                option['icon'] as IconData,
                size: 16,
                color: isSelected ? Colors.white : toggleColors[index],
              ),
              const SizedBox(width: 6),
              Text(
                option['title'] as String,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                  color: isSelected ? Colors.white : toggleColors[index],
                ),
              ),
            ],
          ),
          selected: isSelected,
          onSelected: (selected) {
            if (selected) {
              setState(() {
                _selectedChartIndex = index;
              });
            }
          },
          selectedColor: toggleColors[index],
          backgroundColor: Colors.white,
          side: BorderSide(
            color: toggleColors[index],
            width: 2,
          ),
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        );
      }).toList(),
    );
  }
  Widget _buildSelectedChart(
    BuildContext context,
    Map<String, int> statusData,
    Map<String, int> categoryData,
    Map<String, int> priorityData,
  ) {
    switch (_selectedChartIndex) {
      case 0:
        return _buildEnhancedChart(
          context,
          'Event Status Distribution',
          _buildUniversalPieChart(statusData, _getEventStatusColors()),
          _buildEnhancedLegend(statusData, _getEventStatusColors()),
          Icons.assignment,
        );
      case 1:
        return _buildEnhancedChart(
          context,
          'Event Category Distribution',
          _buildUniversalPieChart(categoryData, _getEventCategoryColors()),
          _buildEnhancedLegend(categoryData, _getEventCategoryColors()),
          Icons.category,
        );
      case 2:
        return _buildEnhancedChart(
          context,
          'Event Priority Distribution',
          _buildUniversalPieChart(priorityData, _getEventPriorityColors()),
          _buildEnhancedLegend(priorityData, _getEventPriorityColors()),
          Icons.priority_high,
        );
      default:
        return _buildEnhancedChart(
          context,
          'Event Status Distribution',
          _buildUniversalPieChart(statusData, _getEventStatusColors()),
          _buildEnhancedLegend(statusData, _getEventStatusColors()),
          Icons.assignment,
        );
    }
  }
  Widget _buildEnhancedChart(
    BuildContext context,
    String title,
    Widget chart,
    Widget? legend,
    IconData icon,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(kBorderRadius * 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(kPaddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: AppColors.eventsHeader.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    icon,
                    color: AppColors.eventsHeader,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: kSpacingLarge),
            SizedBox(height: _chartHeight, child: Center(child: chart)),
            if (legend != null) ...[
              const SizedBox(height: kSpacingMedium),
              legend,
            ],
          ],
        ),
      ),
    );
  }
  Widget _buildPerformanceMetrics(BuildContext context) {
    final colors = _getPerformanceColors();
    final performanceCards = _buildPerformanceCards(colors);
    return _buildGridSection(
      title: 'Performance Metrics',
      subtitle: 'Event completion and timing analysis',
      icon: Icons.timeline,
      headerColor: Colors.blue,
      cardData: performanceCards.map((card) => card.toMap()).toList(),
    );
  }
  /// Build performance metrics cards
  List<InfoCardData> _buildPerformanceCards(List<Color> colors) {
    return [
      InfoCardData(
        title: 'Avg Completion',
        value: '${widget.controller.averageCompletionTime.toStringAsFixed(1)} days',
        subtitle: 'completion time',
        icon: Icons.timer,
        color: colors[0],
        insight: 'Average time to complete events',
      ),
      InfoCardData(
        title: 'This Week',
        value: widget.controller.eventsThisWeek.toString(),
        subtitle: 'weekly events',
        icon: Icons.calendar_view_week,
        color: colors[1],
        insight: 'Current week activity',
      ),
      InfoCardData(
        title: 'Next Week',
        value: widget.controller.eventsNextWeek.toString(),
        subtitle: 'upcoming events',
        icon: Icons.next_week,
        color: colors[2],
        insight: 'Planned for next week',
      ),
      InfoCardData(
        title: 'Event Types',
        value: '${widget.controller.activeEventTypes}/${widget.controller.totalEventTypes}',
        subtitle: 'active types',
        icon: Icons.category,
        color: colors[3],
        insight: 'Active event categories',
      ),
    ];
  }
  Widget _buildCattleSpecificAnalytics(BuildContext context) {
    final colors = _getCattleAnalyticsColors();
    final cattleCards = _buildCattleAnalyticsCards(colors);
    return _buildGridSection(
      title: 'Cattle-Specific Analytics',
      subtitle: 'Event distribution and insights per cattle',
      icon: Icons.pets,
      headerColor: Colors.green,
      cardData: cattleCards.map((card) => card.toMap()).toList(),
    );
  }
  /// Build cattle-specific analytics cards
  List<InfoCardData> _buildCattleAnalyticsCards(List<Color> colors) {
    return [
      InfoCardData(
        title: 'Cattle with Events',
        value: widget.controller.cattleWithEvents.toString(),
        subtitle: 'active cattle',
        icon: Icons.pets,
        color: colors[0],
        insight: 'Cattle with recorded events',
      ),
      InfoCardData(
        title: 'Avg Events/Cattle',
        value: widget.controller.averageEventsPerCattle.toStringAsFixed(1),
        subtitle: 'events per cattle',
        icon: Icons.analytics,
        color: colors[1],
        insight: 'Average events per cattle',
      ),
      InfoCardData(
        title: 'Recurring Events',
        value: widget.controller.recurringEvents.toString(),
        subtitle: 'automated events',
        icon: Icons.repeat,
        color: colors[2],
        insight: 'Recurring event instances',
      ),
      InfoCardData(
        title: 'One-time Events',
        value: widget.controller.oneTimeEvents.toString(),
        subtitle: 'single events',
        icon: Icons.event_note,
        color: colors[3],
        insight: 'Non-recurring events',
      ),
    ];
  }
  Widget _buildCostAnalysis(BuildContext context) {
    final colors = _getCostAnalysisColors();
    final costCards = _buildCostAnalysisCards(colors);
    return _buildGridSection(
      title: 'Cost Analysis',
      subtitle: 'Financial tracking and cost metrics',
      icon: Icons.attach_money,
      headerColor: Colors.purple,
      cardData: costCards.map((card) => card.toMap()).toList(),
    );
  }
  /// Build cost analysis cards
  List<InfoCardData> _buildCostAnalysisCards(List<Color> colors) {
    return [
      InfoCardData(
        title: 'Estimated Costs',
        value: 'Â£${widget.controller.totalEstimatedCosts.toStringAsFixed(0)}',
        subtitle: 'total estimated',
        icon: Icons.calculate,
        color: colors[0],
        insight: 'Total estimated event costs',
      ),
      InfoCardData(
        title: 'Actual Costs',
        value: 'Â£${widget.controller.totalActualCosts.toStringAsFixed(0)}',
        subtitle: 'total actual',
        icon: Icons.receipt,
        color: colors[1],
        insight: 'Total actual event costs',
      ),
      InfoCardData(
        title: 'Avg Event Cost',
        value: 'Â£${widget.controller.averageEventCost.toStringAsFixed(0)}',
        subtitle: 'per event',
        icon: Icons.trending_up,
        color: colors[2],
        insight: 'Average cost per event',
      ),
      InfoCardData(
        title: 'Attachments',
        value: '${widget.controller.eventsWithAttachments}/${widget.controller.totalEvents}',
        subtitle: 'events with files',
        icon: Icons.attach_file,
        color: colors[3],
        insight: 'Events with attachments',
      ),
    ];
  }
  Widget _buildAutomationInsights(BuildContext context) {
    final colors = _getAutomationColors();
    final automationCards = _buildAutomationCards(colors);
    return _buildGridSection(
      title: 'Automation Insights',
      subtitle: 'Automated vs manual event creation analysis',
      icon: Icons.smart_toy,
      headerColor: Colors.orange,
      cardData: automationCards.map((card) => card.toMap()).toList(),
    );
  }
  /// Build automation insights cards
  List<InfoCardData> _buildAutomationCards(List<Color> colors) {
    return [
      InfoCardData(
        title: 'Auto-Generated',
        value: widget.controller.autoGeneratedEvents.toString(),
        subtitle: 'automated events',
        icon: Icons.auto_awesome,
        color: colors[0],
        insight: 'System-generated events',
      ),
      InfoCardData(
        title: 'Manual Events',
        value: widget.controller.manualEvents.toString(),
        subtitle: 'user-created',
        icon: Icons.person,
        color: colors[1],
        insight: 'Manually created events',
      ),
      InfoCardData(
        title: 'Automation Rate',
        value: '${widget.controller.automationRate.toStringAsFixed(1)}%',
        subtitle: 'automation level',
        icon: Icons.trending_up,
        color: colors[2],
        insight: 'Percentage of automated events',
      ),
      InfoCardData(
        title: 'Total Attachments',
        value: widget.controller.totalAttachments.toString(),
        subtitle: 'files attached',
        icon: Icons.folder,
        color: colors[3],
        insight: 'Total event attachments',
      ),
    ];
  }
  Widget _buildEnhancedLegend(Map<String, int> data, Map<String, Color> colors) {
    return Wrap(
      spacing: 12,
      runSpacing: 8,
      children: data.entries.map((entry) {
        final percentage = widget.controller.totalEvents > 0
          ? ((entry.value / widget.controller.totalEvents) * 100).toStringAsFixed(1)
          : '0';
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: colors[entry.key]?.withValues(alpha: 0.1) ?? _fallbackColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 10,
                height: 10,
                decoration: BoxDecoration(
                  color: colors[entry.key] ?? _fallbackColor,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 6),
              Text(
                '${entry.key} (${entry.value}) $percentage%',
                style: const TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
  // Color Management - Different colors for each section
  List<Color> _getKPIColors() => AppColors.eventsKpiColors;
  List<Color> _getPerformanceColors() => AppColors.eventsKpiColors;
  List<Color> _getCattleAnalyticsColors() => AppColors.eventsKpiColors;
  List<Color> _getCostAnalysisColors() => AppColors.eventsKpiColors;
  List<Color> _getAutomationColors() => AppColors.eventsKpiColors;
  Map<String, Color> _getEventStatusColors() {
    return {
      'Scheduled': const Color(0xFF2196F3), // Blue
      'In Progress': const Color(0xFFFF9800), // Orange
      'Completed': const Color(0xFF4CAF50), // Green
      'Cancelled': const Color(0xFF9E9E9E), // Grey
      'Overdue': const Color(0xFFF44336), // Red
      'Missed': const Color(0xFF9C27B0), // Purple
    };
  }
  Map<String, Color> _getEventCategoryColors() {
    return {
      'Health': const Color(0xFFFF5722), // Red-orange
      'Breeding': const Color(0xFFE91E63), // Pink
      'Feeding': const Color(0xFF4CAF50), // Green
      'Management': const Color(0xFF2196F3), // Blue
      'Maintenance': const Color(0xFFFF9800), // Orange
      'Financial': const Color(0xFF9C27B0), // Purple
      'Other': const Color(0xFF607D8B), // Blue-grey
    };
  }
  Map<String, Color> _getEventPriorityColors() {
    return {
      'Low': const Color(0xFF4CAF50), // Green
      'Medium': const Color(0xFFFF9800), // Orange
      'High': const Color(0xFFFF5722), // Red-orange
      'Critical': const Color(0xFFF44336), // Red
    };
  }
  Widget _buildExportSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UniversalFormField.sectionHeader(
          title: 'Export Analytics',
          icon: Icons.file_download,
          color: AppColors.eventsHeader,
          subtitle: 'Export event analytics data and reports',
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(kBorderRadius * 2),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          padding: const EdgeInsets.all(kPaddingMedium),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _exportAnalyticsData(context, 'csv'),
                      icon: const Icon(Icons.table_chart),
                      label: const Text('Export CSV'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.eventsHeader,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: kSpacingMedium),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _exportAnalyticsData(context, 'pdf'),
                      icon: const Icon(Icons.picture_as_pdf),
                      label: const Text('Export PDF'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.eventsHeader,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: kSpacingSmall),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _shareAnalytics(context),
                      icon: const Icon(Icons.share),
                      label: const Text('Share Report'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.eventsHeader,
                        side: const BorderSide(color: AppColors.eventsHeader),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
  void _exportAnalyticsData(BuildContext context, String format) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );
      String? filePath;
      
      if (format.toLowerCase() == 'csv') {
        // Export to CSV - simplified implementation
        final directory = await getApplicationDocumentsDirectory();
        final fileName = 'event_analytics_${DateTime.now().millisecondsSinceEpoch}.csv';
        filePath = '${directory.path}/$fileName';

        // Simple CSV export
        final events = widget.controller.unfilteredEvents;
        final csvContent = _generateCSVContent(events);
        final file = File(filePath);
        await file.writeAsString(csvContent);
      } else if (format.toLowerCase() == 'pdf') {
        // Export to PDF - simplified implementation
        final directory = await getApplicationDocumentsDirectory();
        final fileName = 'event_analytics_${DateTime.now().millisecondsSinceEpoch}.pdf';
        filePath = '${directory.path}/$fileName';

        // Simple PDF placeholder - would need pdf package for full implementation
        final file = File(filePath);
        await file.writeAsString('Event Analytics Report - PDF export not implemented yet');
      }
      Navigator.of(context).pop(); // Close loading dialog
      if (filePath != null) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Analytics exported as ${format.toUpperCase()} successfully!'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'Share',
              textColor: Colors.white,
              onPressed: () => _shareFile(context, filePath!),
            ),
          ),
        );
      }
    } catch (e) {
      Navigator.of(context).pop(); // Close loading dialog
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Export failed: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  void _shareAnalytics(BuildContext context) {
    // Show sharing options
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(kPaddingMedium),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.email),
              title: const Text('Email Report'),
              onTap: () {
                Navigator.pop(context);
                _shareViaEmail(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('Share File'),
              onTap: () {
                Navigator.pop(context);
                _exportAndShare(context);
              },
            ),
          ],
        ),
      ),
    );
  }
  void _shareViaEmail(BuildContext context) async {
    try {
      // Show email input dialog
      final emailController = TextEditingController();
      final result = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Email Report'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Enter email address to send the report:'),
              const SizedBox(height: 16),
              TextField(
                controller: emailController,
                decoration: const InputDecoration(
                  labelText: 'Email Address',
                  hintText: '<EMAIL>',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.emailAddress,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, emailController.text),
              child: const Text('Send'),
            ),
          ],
        ),
      );
      if (result != null && result.isNotEmpty) {
        // Generate PDF report
        _prepareAnalyticsData();

        // Simplified email implementation
        final events = widget.controller.unfilteredEvents;
        final success = await _sendEmailReport(result, events);
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Report sent successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to send email. Please try again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error sending email: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  void _exportAndShare(BuildContext context) async {
    try {
      // Generate PDF report
      _prepareAnalyticsData();
      
      // Simplified PDF export
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'event_analytics_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final filePath = '${directory.path}/$fileName';

      // Simple PDF placeholder
      final file = File(filePath);
      await file.writeAsString('Event Analytics Report - PDF export not implemented yet');
      // Share the file
      await _shareFile(context, filePath);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error sharing report: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  Future<void> _shareFile(BuildContext context, String filePath) async {
    try {
      await Share.shareXFiles(
        [XFile(filePath)],
        text: 'Event Analytics Report',
        subject: 'Cattle Manager - Event Analytics Report',
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error sharing file: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  Map<String, dynamic> _prepareAnalyticsData() {
    return {
      'Total Events': widget.controller.totalEvents,
      'Completed Events': widget.controller.completedEvents,
      'Scheduled Events': widget.controller.scheduledEvents,
      'Overdue Events': widget.controller.overdueEvents,
      'Completion Rate': '${widget.controller.completionRate.toStringAsFixed(1)}%',
      'Events This Month': widget.controller.eventsThisMonth,
      'Average Completion Time': '${widget.controller.averageCompletionTime.toStringAsFixed(1)} days',
      'Cattle with Events': widget.controller.cattleWithEvents,
      'Average Events per Cattle': widget.controller.averageEventsPerCattle.toStringAsFixed(1),
      'Total Estimated Costs': 'Â£${widget.controller.totalEstimatedCosts.toStringAsFixed(2)}',
      'Total Actual Costs': 'Â£${widget.controller.totalActualCosts.toStringAsFixed(2)}',
      'Auto-Generated Events': widget.controller.autoGeneratedEvents,
      'Manual Events': widget.controller.manualEvents,
      'Automation Rate': '${widget.controller.automationRate.toStringAsFixed(1)}%',
    };
  }

  // Helper methods to replace removed services
  String _generateCSVContent(List<dynamic> events) {
    final buffer = StringBuffer();
    buffer.writeln('Title,Description,Cattle,Status,Priority,Scheduled Date,Completed Date');

    for (final event in events) {
      buffer.writeln([
        event.title,
        event.description ?? '',
        event.cattleTagId,
        event.status.toString(),
        event.priority.toString(),
        event.scheduledDate?.toIso8601String() ?? '',
        event.completedDate?.toIso8601String() ?? '',
      ].join(','));
    }

    return buffer.toString();
  }

  Future<bool> _sendEmailReport(String email, List<dynamic> events) async {
    // Simplified email implementation - would need email package
    debugPrint('Would send email report to: $email with ${events.length} events');
    return true; // Placeholder
  }
}
