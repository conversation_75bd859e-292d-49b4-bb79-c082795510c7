import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'dart:async';
import '../models/notification_isar.dart';
import '../models/notification_status.dart';
import '../models/notification_priority.dart';
import '../services/notification_repository.dart';

/// Controller for the notification center screen with selection and bulk operations
class NotificationCenterController extends ChangeNotifier {
  final NotificationRepository _repository = GetIt.instance<NotificationRepository>();
  
  // State variables
  List<NotificationIsar> _notifications = [];
  bool _isLoading = false;
  String? _error;
  bool _hasMoreData = false;
  int _currentPage = 0;
  static const int _pageSize = 20;
  
  // Selection state
  bool _isSelectionMode = false;
  final Set<String> _selectedNotificationIds = <String>{};
  
  // Filter state
  String? _selectedCategory;
  NotificationStatus? _selectedStatus;
  NotificationPriority? _selectedPriority;
  String _searchQuery = '';
  
  // Stream subscription
  StreamSubscription<List<NotificationIsar>>? _notificationSubscription;
  
  // Getters
  List<NotificationIsar> get notifications => _notifications;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasMoreData => _hasMoreData;
  bool get isSelectionMode => _isSelectionMode;
  Set<String> get selectedNotificationIds => _selectedNotificationIds;
  String? get selectedCategory => _selectedCategory;
  NotificationStatus? get selectedStatus => _selectedStatus;
  NotificationPriority? get selectedPriority => _selectedPriority;
  String get searchQuery => _searchQuery;
  
  /// Initialize the controller
  Future<void> initialize() async {
    await _loadNotifications();
    _setupNotificationStream();
  }
  
  /// Setup real-time notification stream
  void _setupNotificationStream() {
    _notificationSubscription?.cancel();
    _notificationSubscription = _repository.watchAllNotifications().listen(
      (notifications) {
        _notifications = _applyFiltersAndSort(notifications);
        _hasMoreData = false; // Reset pagination when data changes
        _currentPage = 0;
        notifyListeners();
      },
      onError: (error) {
        _error = error.toString();
        _isLoading = false;
        notifyListeners();
      },
    );
  }
  
  /// Load notifications with pagination
  Future<void> _loadNotifications() async {
    if (_isLoading) return;
    
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final allNotifications = await _repository.getAllNotifications();
      _notifications = _applyFiltersAndSort(allNotifications);
      _hasMoreData = _notifications.length > _pageSize;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }
  
  /// Apply filters and sorting to notifications
  List<NotificationIsar> _applyFiltersAndSort(List<NotificationIsar> notifications) {
    var filtered = notifications.where((notification) {
      // Category filter
      if (_selectedCategory != null && 
          _selectedCategory!.isNotEmpty && 
          notification.category != _selectedCategory) {
        return false;
      }
      
      // Status filter
      if (_selectedStatus != null && notification.status != _selectedStatus) {
        return false;
      }
      
      // Priority filter
      if (_selectedPriority != null && notification.priority != _selectedPriority) {
        return false;
      }
      
      // Search filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final title = notification.title?.toLowerCase() ?? '';
        final message = notification.message?.toLowerCase() ?? '';
        if (!title.contains(query) && !message.contains(query)) {
          return false;
        }
      }
      
      return true;
    }).toList();
    
    // Sort by creation date (newest first)
    filtered.sort((a, b) {
      final aDate = a.createdAt ?? DateTime.fromMillisecondsSinceEpoch(0);
      final bDate = b.createdAt ?? DateTime.fromMillisecondsSinceEpoch(0);
      return bDate.compareTo(aDate);
    });
    
    return filtered;
  }
  
  /// Load more notifications (pagination)
  Future<void> loadMoreNotifications() async {
    if (_isLoading || !_hasMoreData) return;
    
    _currentPage++;
    // For now, just disable loading more as we load all at once
    // In a real implementation, you'd load the next page from the repository
  }
  
  /// Refresh notifications
  Future<void> refreshNotifications() async {
    _currentPage = 0;
    await _loadNotifications();
  }
  
  /// Toggle selection mode
  void toggleSelectionMode() {
    _isSelectionMode = !_isSelectionMode;
    if (!_isSelectionMode) {
      _selectedNotificationIds.clear();
    }
    notifyListeners();
  }
  
  /// Select all notifications
  void selectAll() {
    _selectedNotificationIds.clear();
    _selectedNotificationIds.addAll(
      _notifications.where((n) => n.businessId != null).map((n) => n.businessId!)
    );
    notifyListeners();
  }
  
  /// Clear selection
  void clearSelection() {
    _selectedNotificationIds.clear();
    notifyListeners();
  }
  
  /// Select/deselect a notification
  void selectNotification(String businessId) {
    if (_selectedNotificationIds.contains(businessId)) {
      _selectedNotificationIds.remove(businessId);
    } else {
      _selectedNotificationIds.add(businessId);
    }
    notifyListeners();
  }
  
  /// Filter by category
  void filterByCategory(String? category) {
    _selectedCategory = category;
    _applyFiltersAndRefresh();
  }
  
  /// Filter by status
  void filterByStatus(NotificationStatus? status) {
    _selectedStatus = status;
    _applyFiltersAndRefresh();
  }
  
  /// Filter by priority
  void filterByPriority(NotificationPriority? priority) {
    _selectedPriority = priority;
    _applyFiltersAndRefresh();
  }
  
  /// Search notifications
  void search(String? query) {
    _searchQuery = query ?? '';
    _applyFiltersAndRefresh();
  }
  
  /// Clear all filters
  void clearAllFilters() {
    _selectedCategory = null;
    _selectedStatus = null;
    _selectedPriority = null;
    _searchQuery = '';
    _applyFiltersAndRefresh();
  }
  
  /// Apply filters and refresh
  void _applyFiltersAndRefresh() {
    _loadNotifications();
  }
  
  /// Mark selected notifications as read
  Future<void> markSelectedAsRead() async {
    for (final businessId in _selectedNotificationIds) {
      try {
        await _repository.markAsRead(businessId);
      } catch (e) {
        debugPrint('Error marking notification as read: $e');
      }
    }
    clearSelection();
  }
  
  /// Archive selected notifications
  Future<void> archiveSelected() async {
    for (final businessId in _selectedNotificationIds) {
      try {
        await _repository.archiveNotification(businessId);
      } catch (e) {
        debugPrint('Error archiving notification: $e');
      }
    }
    clearSelection();
  }
  
  /// Delete selected notifications
  Future<void> deleteSelected() async {
    for (final businessId in _selectedNotificationIds) {
      try {
        await _repository.deleteNotificationByBusinessId(businessId);
      } catch (e) {
        debugPrint('Error deleting notification: $e');
      }
    }
    clearSelection();
  }
  
  @override
  void dispose() {
    _notificationSubscription?.cancel();
    super.dispose();
  }
}
