import 'dart:async';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';

import '../models/help_article_isar.dart';
import '../services/help_repository.dart';
import '../services/help_analytics_service.dart';
import '../../../core/base/base_controller.dart';

/// Filter state object for decoupled filter management
/// Following the established FilterController pattern for consistency
class HelpFilterState {
  final String? searchQuery;
  final String? category;
  final bool? isActive;

  const HelpFilterState({
    this.searchQuery,
    this.category,
    this.isActive,
  });

  /// Check if any filters are active
  bool get hasActiveFilters =>
      searchQuery?.isNotEmpty == true ||
      category?.isNotEmpty == true ||
      isActive != null;

  /// Create empty filter state
  static const HelpFilterState empty = HelpFilterState();
}

/// Reactive controller for the main help screen using Dual-Stream Pattern
/// Following the cattle module template: separate unfiltered and filtered streams
/// Unfiltered stream feeds analytics, filtered stream feeds UI
class HelpController extends BaseController {
  // Use lazy getters to avoid accessing GetIt services in constructor
  final HelpRepository _helpRepository = GetIt.instance<HelpRepository>();
  Isar get _isar => GetIt.instance<Isar>();

  // Stream subscriptions for real-time updates - Separated for filtered/unfiltered data
  StreamSubscription<List<HelpArticleIsar>>? _unfilteredStreamSubscription;
  StreamSubscription<List<HelpArticleIsar>>? _filteredStreamSubscription;

  // Data storage - Separated for analytics and UI
  List<HelpArticleIsar> _unfilteredArticles = [];
  List<HelpArticleIsar> _filteredArticles = [];

  // Analytics result
  HelpAnalyticsResult _analyticsResult = HelpAnalyticsResult.empty;

  // Filter management
  HelpFilterState _currentFilters = HelpFilterState.empty;
  bool _hasActiveFilters = false;

  // Getters
  List<HelpArticleIsar> get articles => _filteredArticles;
  HelpAnalyticsResult get analytics => _analyticsResult;
  HelpFilterState get currentFilters => _currentFilters;
  bool get hasActiveFilters => _hasActiveFilters;

  /// Initialize controller and start stream listeners
  HelpController() {
    _initializeStreamListeners();
  }

  @override
  Future<void> loadData() async {
    // Data loading is handled by the streams in _initializeStreamListeners
    // This method is required by BaseController but can be empty if streams handle loading
    setState(ControllerState.loaded);
  }

  @override
  Future<void> refresh() async {
    // Force analytics recalculation on unfiltered data
    _updateAnalytics();
    notifyListeners();
  }

  /// Initialize stream listeners for real-time updates using Isar's native watch
  /// Critical: Separate streams for unfiltered (analytics) and filtered (UI) data
  void _initializeStreamListeners() {
    // Primary stream: Unfiltered data for analytics calculations
    // This stream NEVER changes and always provides the complete dataset
    _unfilteredStreamSubscription = _helpRepository.watchAllHelpArticles()
        .listen((unfilteredList) {
      _handleUnfilteredDataUpdate(unfilteredList);
    });

    // Initially, filtered data equals unfiltered data (no filters applied)
    _filteredArticles = _unfilteredArticles;
    _hasActiveFilters = false;

    // Add subscriptions for proper disposal
    addSubscription(_unfilteredStreamSubscription!);
  }

  /// Handle unfiltered data update and recalculate analytics
  void _handleUnfilteredDataUpdate(List<HelpArticleIsar> unfilteredList) {
    _unfilteredArticles = unfilteredList;
    
    // Always calculate analytics on unfiltered data
    _updateAnalytics();
    
    // Update filtered data if no active filters
    if (!_hasActiveFilters) {
      _filteredArticles = List.from(_unfilteredArticles);
    }
    
    setState(ControllerState.loaded);
  }

  /// Update analytics calculations using unfiltered data only
  void _updateAnalytics() {
    _analyticsResult = HelpAnalyticsService.calculate(_unfilteredArticles);
  }

  /// Apply filters using the FilterController pattern for decoupled filter management
  /// Critical Fix: This method now creates a separate filtered stream without affecting analytics
  void applyFilters(HelpFilterState filterState) async {
    // Update current filter state
    _currentFilters = filterState;

    // Cancel existing filtered subscription (but keep unfiltered stream for analytics)
    _filteredStreamSubscription?.cancel();

    // Check if filters are active
    _hasActiveFilters = filterState.hasActiveFilters;

    if (_hasActiveFilters) {
      // Build filtered query for UI display
      final filteredQuery = _buildFilteredQuery(filterState);

      // Create separate stream for filtered data
      _filteredStreamSubscription = filteredQuery.watch(fireImmediately: true)
          .listen((filteredList) {
        _filteredArticles = filteredList;
        notifyListeners();
      });

      // Add subscription for proper disposal
      addSubscription(_filteredStreamSubscription!);
    } else {
      // No filters: filtered data equals unfiltered data
      _filteredArticles = List.from(_unfilteredArticles);
      notifyListeners();
    }
  }

  /// Build filtered Isar query based on FilterState object
  /// This method dynamically constructs database queries for optimal performance
  dynamic _buildFilteredQuery(HelpFilterState filterState) {
    // Start with base query
    dynamic query = _isar.helpArticleIsars.where();

    // Apply search filter at database level
    if (filterState.searchQuery?.isNotEmpty == true) {
      final searchTerm = filterState.searchQuery!.toLowerCase();
      query = query.filter().group((q) => q
          .titleContains(searchTerm, caseSensitive: false)
          .or()
          .contentContains(searchTerm, caseSensitive: false)
          .or()
          .searchKeywordsContains(searchTerm, caseSensitive: false));
    }

    // Apply category filter
    if (filterState.category?.isNotEmpty == true && filterState.category != 'All') {
      query = query.filter().categoryEqualTo(filterState.category);
    }

    // Apply active status filter
    if (filterState.isActive != null) {
      query = query.filter().isActiveEqualTo(filterState.isActive!);
    }

    // Apply sorting at database level for optimal performance
    return query.sortByPriorityDesc().thenByCreatedAtDesc();
  }

  /// Clear all filters and show all data
  void clearFilters() {
    applyFilters(HelpFilterState.empty);
  }

  /// Get available categories for filter dropdown
  List<String> getAvailableCategories() {
    final categories = _unfilteredArticles
        .where((article) => article.category?.isNotEmpty == true)
        .map((article) => article.category!)
        .toSet()
        .toList();
    
    categories.sort();
    return categories;
  }

  @override
  void dispose() {
    _unfilteredStreamSubscription?.cancel();
    _filteredStreamSubscription?.cancel();
    super.dispose();
  }
}
