import 'package:logging/logging.dart';
import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';

import 'package:flutter/foundation.dart';
// Core services
import '../services/database/isar_service.dart';

// Models
import '../Dashboard/Farm Setup/models/farm_isar.dart';
import '../Dashboard/Farm Setup/models/animal_type_isar.dart';
import '../Dashboard/Farm Setup/models/breed_category_isar.dart';



// Services
import '../Dashboard/Farm Setup/services/farm_setup_repository.dart';
// import '../Dashboard/Events/services/events_repository.dart';
import '../Dashboard/Transactions/services/transactions_repository.dart';
import '../Dashboard/Transactions/models/category_isar.dart';
// import '../Dashboard/Settings/services/settings_repository.dart';

// Constants
import '../constants/app_icons.dart';

/// Safe data seeding service that replaces the dangerous logic in IsarInitializer
/// Implements proper migration patterns instead of destructive clear() operations
class DefaultDataSeeder {
  static final Logger _logger = Logger('DefaultDataSeeder');

  /// Current database version - increment when adding new default data
  static const int currentDatabaseVersion = 1;

  final IsarService _isarService;
  final FarmSetupRepository _farmSetupRepository;
  // final EventsRepository _eventsRepository;
  final TransactionsRepository _transactionsRepository;
  // final SettingsRepository _settingsRepository;

  /// Constructor with explicit dependencies (Pure DI pattern)
  DefaultDataSeeder(
    this._isarService,
    this._farmSetupRepository,
    // this._eventsRepository,
    this._transactionsRepository,
    // this._settingsRepository,
  );

  /// Seed all default data safely with version-based migrations
  /// This replaces IsarInitializer.ensureDefaultData()
  Future<void> seedInitialData() async {
    try {
      debugPrint('🌱 [SEED_DEBUG] DefaultDataSeeder.seedInitialData() called at ${DateTime.now()}');
      _logger.info('Starting data seeding and migration...');

      // Get current database version (using SharedPreferences since AppSettingsIsar was removed)
      debugPrint('🌱 [SEED_DEBUG] Getting or creating app settings...');
      await _getOrCreateAppSettings();
      const savedVersion = 0; // Default to 0 since AppSettingsIsar was removed
      debugPrint('🌱 [SEED_DEBUG] Saved version: $savedVersion, target version: $currentDatabaseVersion');

      _logger.info('Current database version: $savedVersion, target version: $currentDatabaseVersion');

      // Run migrations based on version
      if (savedVersion < 1) {
        debugPrint('🌱 [SEED_DEBUG] Running initial data seeding (version 0 -> 1)...');
        _logger.info('Running initial data seeding (version 0 -> 1)');
        await _seedVersion1Data();
        debugPrint('✅ [SEED_DEBUG] Version 1 data seeding completed');
      } else {
        debugPrint('🌱 [SEED_DEBUG] Database already at version $savedVersion, skipping seeding');
      }

      // Future migrations would go here:
      // if (savedVersion < 2) {
      //   _logger.info('Running migration (version 1 -> 2)');
      //   await _seedVersion2Data();
      // }

      // Update database version
      debugPrint('🌱 [SEED_DEBUG] Updating database version to $currentDatabaseVersion...');
      await _setDatabaseVersion(currentDatabaseVersion);

      debugPrint('✅ [SEED_DEBUG] Data seeding and migration completed successfully');
      _logger.info('Data seeding and migration completed successfully');
    } catch (e) {
      debugPrint('❌ [SEED_DEBUG] Error during data seeding: $e');
      debugPrint('❌ [SEED_DEBUG] Error type: ${e.runtimeType}');
      debugPrint('❌ [SEED_DEBUG] Stack trace: ${StackTrace.current}');

      _logger.severe('Error during data seeding: $e');
      rethrow;
    }
  }

  /// Note: App settings functionality removed as AppSettingsIsar was deleted
  /// Version tracking is now handled through SharedPreferences or other means
  Future<void> _getOrCreateAppSettings() async {
    // No-op: AppSettingsIsar was removed during cleanup
    _logger.info('Skipping app settings creation (AppSettingsIsar removed)');
  }

  /// Update the database version after successful migration
  /// Note: Version tracking simplified since AppSettingsIsar was removed
  Future<void> _setDatabaseVersion(int version) async {
    try {
      // For now, we'll just log the version. In the future, this could use SharedPreferences
      _logger.info('Database version would be set to $version (AppSettingsIsar removed)');
    } catch (e) {
      _logger.severe('Error setting database version: $e');
      rethrow;
    }
  }

  /// Seed all initial data for version 1
  Future<void> _seedVersion1Data() async {
    await _ensureDefaultFarm();
    await _ensureDefaultAnimalTypes();
    await _ensureDefaultBreedCategories();
    await _ensureDefaultEventTypes();
    await _ensureDefaultTransactionCategories();
  }

  /// Generic helper method to reduce code duplication in seeding operations
  Future<void> _seedData<T>({
    required Future<List<T>> Function() fetchAll,
    required List<T> Function() createDefaults,
    required IsarCollection<T> collection,
    required String entityName,
  }) async {
    try {
      final items = await fetchAll();
      if (items.isEmpty) {
        _logger.info('No $entityName found, creating defaults');
        final defaults = createDefaults();

        await _isarService.isar.writeTxn(() async {
          await collection.putAll(defaults);
        });

        _logger.info('Created ${defaults.length} default $entityName');
      } else {
        _logger.info('$entityName already exist: ${items.length} found');
      }
    } catch (e) {
      _logger.severe('Error ensuring default $entityName: $e');
      rethrow;
    }
  }

  /// Ensure default farm exists
  Future<void> _ensureDefaultFarm() async {
    try {
      debugPrint('🏠 [FARM_DEBUG] _ensureDefaultFarm() called');

      // Check if any farms exist
      debugPrint('🏠 [FARM_DEBUG] Checking for existing farms...');
      final existingFarms = await _farmSetupRepository.getAllFarms();
      debugPrint('🏠 [FARM_DEBUG] Found ${existingFarms.length} existing farms');

      if (existingFarms.isNotEmpty) {
        // Check if the existing farm has a valid farmBusinessId
        final existingFarm = existingFarms.first;
        debugPrint('🏠 [FARM_DEBUG] Existing farm: ${existingFarm.name} (ID: ${existingFarm.farmBusinessId})');

        if (existingFarm.farmBusinessId == null || existingFarm.farmBusinessId!.isEmpty) {
          debugPrint('🏠 [FARM_DEBUG] Existing farm has null/empty farmBusinessId, fixing...');

          // Generate a new business ID for the existing farm
          final newBusinessId = const Uuid().v4();
          existingFarm.farmBusinessId = newBusinessId;
          existingFarm.updatedAt = DateTime.now();

          debugPrint('🏠 [FARM_DEBUG] Generated new business ID: $newBusinessId');

          // Update the farm in the database
          await _farmSetupRepository.updateFarm(existingFarm);

          debugPrint('✅ [FARM_DEBUG] Fixed existing farm with new business ID: $newBusinessId');
          _logger.info('Fixed existing farm with new business ID: $newBusinessId');
        } else {
          debugPrint('🏠 [FARM_DEBUG] Existing farm has valid business ID, no action needed');
          _logger.info('Default farm already exists, skipping creation');
        }
        return;
      }

      debugPrint('🏠 [FARM_DEBUG] No existing farms found, creating default farm...');

      // Create default farm using the repository method to ensure proper ID generation
      final farmId = const Uuid().v4();
      debugPrint('🏠 [FARM_DEBUG] Generated farm business ID: $farmId');

      final defaultFarm = FarmIsar.create(
        id: farmId, // Generate unique business ID
        name: 'My Farm',
        ownerName: 'Farm Owner',
        ownerContact: '+1234567890',
        ownerEmail: '<EMAIL>',
        farmType: FarmType.dairy,
        cattleCount: 0,
        capacity: 100,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      debugPrint('🏠 [FARM_DEBUG] Default farm object created: ${defaultFarm.name}');
      debugPrint('🏠 [FARM_DEBUG] Farm details - Name: ${defaultFarm.name}, ID: ${defaultFarm.farmBusinessId}, Owner: ${defaultFarm.ownerName}');

      // Use repository method to add farm (ensures proper validation and ID handling)
      debugPrint('🏠 [FARM_DEBUG] Adding farm to repository...');
      await _farmSetupRepository.addFarm(defaultFarm);

      debugPrint('✅ [FARM_DEBUG] Default farm created successfully: ${defaultFarm.name} (ID: ${defaultFarm.farmBusinessId})');
      _logger.info('Default farm created successfully: ${defaultFarm.name}');
    } catch (e) {
      debugPrint('❌ [FARM_DEBUG] Error creating default farm: $e');
      debugPrint('❌ [FARM_DEBUG] Error type: ${e.runtimeType}');
      debugPrint('❌ [FARM_DEBUG] Stack trace: ${StackTrace.current}');

      _logger.severe('Error creating default farm: $e');
      // Don't rethrow - allow app to continue even if farm creation fails
    }
  }

  /// Ensure default animal types exist with proper icons
  Future<void> _ensureDefaultAnimalTypes() async {
    await _seedData<AnimalTypeIsar>(
      fetchAll: () => _farmSetupRepository.getAllAnimalTypes(),
      createDefaults: () => [
        AnimalTypeIsar()
          ..name = 'Cow'
          ..iconCodePoint = AppIcons.cow.codePoint
          ..iconFontFamily = AppIcons.cow.fontFamily
          ..colorValue = AppIcons.getAnimalColor('Cow').value // ignore: deprecated_member_use (value is correct for storing ARGB int)
          ..defaultGestationDays = 283
          ..defaultHeatCycleDays = 21
          ..defaultEmptyPeriodDays = 60
          ..defaultBreedingAge = 450
          ..businessId = AnimalTypeIsar.cowId,
        AnimalTypeIsar()
          ..name = 'Buffalo'
          ..iconCodePoint = AppIcons.buffalo.codePoint
          ..iconFontFamily = AppIcons.buffalo.fontFamily
          ..colorValue = AppIcons.getAnimalColor('Buffalo').value // ignore: deprecated_member_use (value is correct for storing ARGB int)
          ..defaultGestationDays = 310
          ..defaultHeatCycleDays = 21
          ..defaultEmptyPeriodDays = 90
          ..defaultBreedingAge = 730
          ..businessId = AnimalTypeIsar.buffaloId,
        AnimalTypeIsar()
          ..name = 'Goat'
          ..iconCodePoint = AppIcons.goat.codePoint
          ..iconFontFamily = AppIcons.goat.fontFamily
          ..colorValue = AppIcons.getAnimalColor('Goat').value // ignore: deprecated_member_use (value is correct for storing ARGB int)
          ..defaultGestationDays = 150
          ..defaultHeatCycleDays = 21
          ..defaultEmptyPeriodDays = 30
          ..defaultBreedingAge = 240
          ..businessId = AnimalTypeIsar.goatId,
        AnimalTypeIsar()
          ..name = 'Sheep'
          ..iconCodePoint = AppIcons.sheep.codePoint
          ..iconFontFamily = AppIcons.sheep.fontFamily
          ..colorValue = AppIcons.getAnimalColor('Sheep').value // ignore: deprecated_member_use (value is correct for storing ARGB int)
          ..defaultGestationDays = 147
          ..defaultHeatCycleDays = 17
          ..defaultEmptyPeriodDays = 30
          ..defaultBreedingAge = 240
          ..businessId = AnimalTypeIsar.sheepId,
      ],
      collection: _isarService.animalTypeIsars,
      entityName: 'animal types',
    );
  }

  /// Ensure default breed categories exist
  Future<void> _ensureDefaultBreedCategories() async {
    try {
      // Check directly in the database for any breed records
      final allBreedsInDb = await _isarService.breedCategoryIsars.where().findAll();
      _logger.info('Direct database query found ${allBreedsInDb.length} breed records');

      if (allBreedsInDb.isNotEmpty) {
        _logger.info('Breed categories already exist: ${allBreedsInDb.length} found');
        for (final breed in allBreedsInDb) {
          _logger.info('  - ${breed.name} (ID: ${breed.businessId})');
        }
        return;
      }

      // Double-check with repository method
      final breeds = await _farmSetupRepository.getAllBreedCategories();
      _logger.info('Repository query found ${breeds.length} existing breed categories');

      if (breeds.isNotEmpty) {
        _logger.info('Breed categories found via repository: ${breeds.length}');
        return;
      }

      // Fetch the animal types we need IN THIS METHOD
      final animalTypes = await _farmSetupRepository.getAllAnimalTypes();
      _logger.info('Found ${animalTypes.length} animal types');
      for (final type in animalTypes) {
        _logger.info('  - ${type.name} (ID: ${type.businessId})');
      }

      if (animalTypes.isEmpty) {
        _logger.warning('Cannot seed breeds because no animal types exist. Please run seeder again.');
        return; // Abort this specific seeding step
      }

      // Create a lookup map for safety
      final animalTypeMap = {
        for (var type in animalTypes) type.name: type.businessId
      };
      _logger.info('Animal type map: $animalTypeMap');

      final List<BreedCategoryIsar> breedsToCreate = [];

      // Use the map to safely get the parent ID
      final cowId = animalTypeMap['Cow'];
      if (cowId != null) {
        breedsToCreate.add(BreedCategoryIsar()
          ..businessId = BreedCategoryIsar.holsteinId
          ..name = 'Holstein'
          ..animalTypeId = cowId
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now());
        breedsToCreate.add(BreedCategoryIsar()
          ..businessId = BreedCategoryIsar.angusId
          ..name = 'Angus'
          ..animalTypeId = cowId
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now());
      }

      final buffaloId = animalTypeMap['Buffalo'];
      if (buffaloId != null) {
        breedsToCreate.add(BreedCategoryIsar()
          ..businessId = BreedCategoryIsar.murrahId
          ..name = 'Murrah'
          ..animalTypeId = buffaloId
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now());
        breedsToCreate.add(BreedCategoryIsar()
          ..businessId = BreedCategoryIsar.niliraviId
          ..name = 'Nili-Ravi'
          ..animalTypeId = buffaloId
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now());
      }

      final goatId = animalTypeMap['Goat'];
      if (goatId != null) {
        breedsToCreate.add(BreedCategoryIsar()
          ..businessId = BreedCategoryIsar.boerId
          ..name = 'Boer'
          ..animalTypeId = goatId
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now());
        breedsToCreate.add(BreedCategoryIsar()
          ..businessId = BreedCategoryIsar.alpineId
          ..name = 'Alpine'
          ..animalTypeId = goatId
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now());
      }

      final sheepId = animalTypeMap['Sheep'];
      if (sheepId != null) {
        breedsToCreate.add(BreedCategoryIsar()
          ..businessId = BreedCategoryIsar.merinoId
          ..name = 'Merino'
          ..animalTypeId = sheepId
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now());
        breedsToCreate.add(BreedCategoryIsar()
          ..businessId = BreedCategoryIsar.suffolkId
          ..name = 'Suffolk'
          ..animalTypeId = sheepId
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now());
      }

      if (breedsToCreate.isNotEmpty) {
        _logger.info('About to create ${breedsToCreate.length} breeds:');
        for (final breed in breedsToCreate) {
          _logger.info('  - ${breed.name} (ID: ${breed.businessId})');
        }

        // Check each breed individually before creating to avoid unique constraint violations
        final breedsToActuallyCreate = <BreedCategoryIsar>[];

        for (final breed in breedsToCreate) {
          // Check if this specific breed already exists by business ID
          final existingBreed = await _isarService.breedCategoryIsars
              .filter()
              .businessIdEqualTo(breed.businessId!)
              .findFirst();

          if (existingBreed == null) {
            breedsToActuallyCreate.add(breed);
            _logger.info('Will create breed: ${breed.name} (ID: ${breed.businessId})');
          } else {
            _logger.info('Breed already exists, skipping: ${breed.name} (ID: ${breed.businessId})');
          }
        }

        if (breedsToActuallyCreate.isNotEmpty) {
          // Create all non-duplicate breeds in a single transaction
          await _isarService.isar.writeTxn(() async {
            await _isarService.breedCategoryIsars.putAll(breedsToActuallyCreate);
          });
          _logger.info('Successfully created ${breedsToActuallyCreate.length} new breed categories');
        } else {
          _logger.info('All breeds already exist, no new breeds created');
        }

        _logger.info('Created ${breedsToCreate.length} default breed categories');
      } else {
        _logger.warning('No breeds created because required animal types were not found');
      }
    } catch (e) {
      _logger.severe('Error ensuring default breed categories: $e');
      rethrow;
    }
  }



  /// Ensure default event types exist
  Future<void> _ensureDefaultEventTypes() async {
    // For now, skip event types creation since the repository method may not be available
    // This can be implemented later when the EventsRepository is properly set up
    _logger.info('Skipping event types creation - method not available');
    return;
  }

  /// Ensure default transaction categories exist
  Future<void> _ensureDefaultTransactionCategories() async {
    try {
      _logger.info('🔍 Checking existing transaction categories...');
      final categories = await _transactionsRepository.watchAllCategories().first;
      _logger.info('📊 Found ${categories.length} existing transaction categories');

      if (categories.isEmpty) {
        _logger.info('✨ No transaction categories found, creating defaults...');
        final defaultCategories = [
        // Income Categories
        CategoryIsar.create(
          categoryId: CategoryIsar.staticIds['milk sales']!,
          name: 'Milk Sales',
          description: 'Revenue from milk production and sales',
          type: 'Income',
          icon: AppIcons.income,
        ),
        CategoryIsar.create(
          categoryId: CategoryIsar.staticIds['animal sales']!,
          name: 'Animal Sales',
          description: 'Revenue from selling livestock',
          type: 'Income',
          icon: AppIcons.income,
        ),
        CategoryIsar.create(
          categoryId: CategoryIsar.staticIds['other income']!,
          name: 'Other Income',
          description: 'Miscellaneous farm income',
          type: 'Income',
          icon: AppIcons.income,
        ),

        // Expense Categories
        CategoryIsar.create(
          categoryId: CategoryIsar.staticIds['feed']!,
          name: 'Feed',
          description: 'Animal feed and nutrition expenses',
          type: 'Expense',
          icon: AppIcons.expense,
        ),
        CategoryIsar.create(
          categoryId: CategoryIsar.staticIds['veterinary']!,
          name: 'Veterinary',
          description: 'Veterinary care and medical expenses',
          type: 'Expense',
          icon: AppIcons.expense,
        ),
        CategoryIsar.create(
          categoryId: CategoryIsar.staticIds['supplies']!,
          name: 'Supplies',
          description: 'Farm supplies and equipment',
          type: 'Expense',
          icon: AppIcons.expense,
        ),
        CategoryIsar.create(
          categoryId: CategoryIsar.staticIds['labor']!,
          name: 'Labor',
          description: 'Labor costs and wages',
          type: 'Expense',
          icon: AppIcons.expense,
        ),
        CategoryIsar.create(
          categoryId: CategoryIsar.staticIds['other expenses']!,
          name: 'Other Expenses',
          description: 'Miscellaneous farm expenses',
          type: 'Expense',
          icon: AppIcons.expense,
        ),
        ];

        _logger.info('💾 Saving ${defaultCategories.length} default categories...');
        for (final category in defaultCategories) {
          _logger.info('  - Creating category: ${category.name} (${category.type})');
          await _transactionsRepository.saveCategory(category);
        }

        _logger.info('✅ Created ${defaultCategories.length} default transaction categories');
      } else {
        _logger.info('📋 Transaction categories already exist: ${categories.length} found');
        for (final category in categories) {
          _logger.info('  - Existing: ${category.name} (${category.type})');
        }
      }
    } catch (e) {
      _logger.severe('Error ensuring default transaction categories: $e');
      rethrow;
    }
  }
}
