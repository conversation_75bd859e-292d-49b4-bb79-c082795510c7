import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';
import 'user_isar.dart';

part 'user_settings_isar.g.dart';

@collection
class UserSettingsIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? businessId;

  @Index(unique: true)
  String? userBusinessId;

  // Notification preferences
  bool emailNotifications = true;
  bool pushNotifications = true;
  bool smsNotifications = false;

  // Notification types
  bool healthReminders = true;
  bool breedingReminders = true;
  bool milkingReminders = true;
  bool vaccinationReminders = true;
  bool eventReminders = true;

  // App preferences
  String theme = 'system'; // 'light', 'dark', 'system'
  String language = 'en';
  String dateFormat = 'dd/MM/yyyy';
  String timeFormat = '24h'; // '12h' or '24h'
  String currency = 'USD';

  // Privacy settings
  bool shareDataForAnalytics = false;
  bool allowLocationTracking = true;
  bool showProfileToOthers = false;

  // Security settings
  bool requireBiometricAuth = false;
  bool autoLockEnabled = true;
  int autoLockTimeoutMinutes = 15;
  bool logSecurityEvents = true;

  // Backup and sync settings
  bool autoBackupEnabled = true;
  String backupFrequency = 'daily'; // 'daily', 'weekly', 'monthly'
  bool syncWithCloud = false;
  String? lastBackupDate;

  // Display preferences
  bool showCattleImages = true;
  bool compactView = false;
  int itemsPerPage = 20;

  // Timestamps
  DateTime? createdAt;
  DateTime? updatedAt;

  // Relationship to user
  @Backlink(to: 'settings')
  final user = IsarLink<UserIsar>();

  UserSettingsIsar();

  factory UserSettingsIsar.createDefault({
    required String userBusinessId,
  }) {
    return UserSettingsIsar()
      ..businessId = const Uuid().v4()
      ..userBusinessId = userBusinessId
      ..emailNotifications = true
      ..pushNotifications = true
      ..smsNotifications = false
      ..healthReminders = true
      ..breedingReminders = true
      ..milkingReminders = true
      ..vaccinationReminders = true
      ..eventReminders = true
      ..theme = 'system'
      ..language = 'en'
      ..dateFormat = 'dd/MM/yyyy'
      ..timeFormat = '24h'
      ..currency = 'USD'
      ..shareDataForAnalytics = false
      ..allowLocationTracking = true
      ..showProfileToOthers = false
      ..requireBiometricAuth = false
      ..autoLockEnabled = true
      ..autoLockTimeoutMinutes = 15
      ..logSecurityEvents = true
      ..autoBackupEnabled = true
      ..backupFrequency = 'daily'
      ..syncWithCloud = false
      ..showCattleImages = true
      ..compactView = false
      ..itemsPerPage = 20
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  // Helper methods
  void updateNotificationSettings({
    bool? emailNotifications,
    bool? pushNotifications,
    bool? smsNotifications,
    bool? healthReminders,
    bool? breedingReminders,
    bool? milkingReminders,
    bool? vaccinationReminders,
    bool? eventReminders,
  }) {
    if (emailNotifications != null) this.emailNotifications = emailNotifications;
    if (pushNotifications != null) this.pushNotifications = pushNotifications;
    if (smsNotifications != null) this.smsNotifications = smsNotifications;
    if (healthReminders != null) this.healthReminders = healthReminders;
    if (breedingReminders != null) this.breedingReminders = breedingReminders;
    if (milkingReminders != null) this.milkingReminders = milkingReminders;
    if (vaccinationReminders != null) this.vaccinationReminders = vaccinationReminders;
    if (eventReminders != null) this.eventReminders = eventReminders;
    updatedAt = DateTime.now();
  }

  void updateAppPreferences({
    String? theme,
    String? language,
    String? dateFormat,
    String? timeFormat,
    String? currency,
  }) {
    if (theme != null) this.theme = theme;
    if (language != null) this.language = language;
    if (dateFormat != null) this.dateFormat = dateFormat;
    if (timeFormat != null) this.timeFormat = timeFormat;
    if (currency != null) this.currency = currency;
    updatedAt = DateTime.now();
  }

  void updatePrivacySettings({
    bool? shareDataForAnalytics,
    bool? allowLocationTracking,
    bool? showProfileToOthers,
  }) {
    if (shareDataForAnalytics != null) this.shareDataForAnalytics = shareDataForAnalytics;
    if (allowLocationTracking != null) this.allowLocationTracking = allowLocationTracking;
    if (showProfileToOthers != null) this.showProfileToOthers = showProfileToOthers;
    updatedAt = DateTime.now();
  }

  void updateSecuritySettings({
    bool? requireBiometricAuth,
    bool? autoLockEnabled,
    int? autoLockTimeoutMinutes,
    bool? logSecurityEvents,
  }) {
    if (requireBiometricAuth != null) this.requireBiometricAuth = requireBiometricAuth;
    if (autoLockEnabled != null) this.autoLockEnabled = autoLockEnabled;
    if (autoLockTimeoutMinutes != null) this.autoLockTimeoutMinutes = autoLockTimeoutMinutes;
    if (logSecurityEvents != null) this.logSecurityEvents = logSecurityEvents;
    updatedAt = DateTime.now();
  }

  void updateBackupSettings({
    bool? autoBackupEnabled,
    String? backupFrequency,
    bool? syncWithCloud,
    String? lastBackupDate,
  }) {
    if (autoBackupEnabled != null) this.autoBackupEnabled = autoBackupEnabled;
    if (backupFrequency != null) this.backupFrequency = backupFrequency;
    if (syncWithCloud != null) this.syncWithCloud = syncWithCloud;
    if (lastBackupDate != null) this.lastBackupDate = lastBackupDate;
    updatedAt = DateTime.now();
  }

  void updateDisplaySettings({
    bool? showCattleImages,
    bool? compactView,
    int? itemsPerPage,
  }) {
    if (showCattleImages != null) this.showCattleImages = showCattleImages;
    if (compactView != null) this.compactView = compactView;
    if (itemsPerPage != null) this.itemsPerPage = itemsPerPage;
    updatedAt = DateTime.now();
  }

  @override
  String toString() {
    return 'UserSettingsIsar(id: $businessId, userId: $userBusinessId, theme: $theme, notifications: $emailNotifications)';
  }
}
