import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'dart:async';
import '../models/notification_isar.dart';
import '../models/notification_filter.dart';
import '../models/notification_status.dart';
import '../services/notification_repository.dart';
import '../services/notification_analytics_service.dart';
import '../../../core/base/base_controller.dart';

/// Filter state for Notifications module
class NotificationFilterState {
  final NotificationStatus? status;
  final String? category;
  final String? cattleId;
  final DateTime? fromDate;
  final DateTime? toDate;
  final String? searchText;

  const NotificationFilterState({
    this.status,
    this.category,
    this.cattleId,
    this.fromDate,
    this.toDate,
    this.searchText,
  });

  /// Empty filter state
  static const NotificationFilterState empty = NotificationFilterState();

  /// Check if filter has any active filters
  bool get hasActiveFilters {
    return status != null ||
           (category?.isNotEmpty ?? false) ||
           (cattleId?.isNotEmpty ?? false) ||
           fromDate != null ||
           toDate != null ||
           (searchText?.isNotEmpty ?? false);
  }

  /// Convert to NotificationFilter for backward compatibility
  NotificationFilter toNotificationFilter() {
    return NotificationFilter(
      status: status,
      category: category,
      cattleId: cattleId,
      fromDate: fromDate,
      toDate: toDate,
    );
  }
}

/// Notifications Controller
///
/// Manages state for the notifications system following established patterns.
/// Implements dual-stream architecture with filtered/unfiltered data separation.
/// Analytics are ALWAYS calculated on unfiltered data for accuracy.
///
/// Follows the standardized DualStreamController pattern from foundation.
class NotificationsController extends DualStreamController<NotificationIsar, NotificationFilterState> {
  // Use lazy getters to avoid accessing GetIt services in constructor
  NotificationRepository get _notificationRepository => GetIt.instance<NotificationRepository>();

  // Analytics result from the dedicated service - ALWAYS calculated on unfiltered data
  NotificationAnalyticsResult _analyticsResult = NotificationAnalyticsResult.empty;

  // Getters
  NotificationAnalyticsResult get analytics => _analyticsResult;

  // Backward compatibility getters
  List<NotificationIsar> get notifications => filteredData;
  NotificationFilter get currentFilterLegacy => currentFilter?.toNotificationFilter() ?? NotificationFilter();

  // Convenience getters from analytics
  int get unreadCount => _analyticsResult.unreadCount;
  int get totalCount => _analyticsResult.totalCount;
  Map<String, int> get categoryCounts => _analyticsResult.categoryCounts;
  Map<NotificationStatus, int> get statusCounts => _analyticsResult.statusCounts;

  //=== CONSTRUCTOR ===//

  NotificationsController() {
    _initializeStreamListeners();
  }

  //=== INITIALIZATION ===//

  /// Initialize and load data
  @override
  Future<void> loadData() async {
    try {
      setState(ControllerState.loading);
      // Data comes from Isar watch() streams automatically
      // State management is handled by stream listeners
    } catch (e, stackTrace) {
      debugPrint('Error loading notifications data: $e\n$stackTrace');
      setError('Failed to load notifications data: ${e.toString()}');
    }
  }

  /// Initialize stream listeners for real-time updates using Isar's native watch
  /// Critical: Separate streams for unfiltered (analytics) data
  void _initializeStreamListeners() {
    // Primary stream: Unfiltered data for analytics calculations
    // This stream NEVER changes and always provides the complete dataset
    final unfilteredStreamSubscription = _notificationRepository.watchAllNotifications()
        .listen((unfilteredList) {
      updateUnfilteredData(unfilteredList);
    });

    // Add the subscription for proper disposal
    addSubscription(unfilteredStreamSubscription);
  }

  //=== ANALYTICS ===//

  /// Update analytics - ALWAYS calculated on unfiltered data
  void _updateAnalytics() {
    _analyticsResult = NotificationAnalyticsService.calculate(unfilteredData);
  }

  //=== FILTERING ===//

  /// Apply current filters to unfiltered data
  void _applyCurrentFilters() {
    if (currentFilter == null || !currentFilter!.hasActiveFilters) {
      // No filters applied - show all data
      updateFilteredData(unfilteredData);
      return;
    }

    // Apply filters to unfiltered data
    final filtered = unfilteredData.where((notification) {
      // Status filter
      if (currentFilter!.status != null && notification.status != currentFilter!.status) {
        return false;
      }

      // Category filter
      if (currentFilter!.category != null &&
          currentFilter!.category!.isNotEmpty &&
          notification.category != currentFilter!.category) {
        return false;
      }

      // Cattle ID filter
      if (currentFilter!.cattleId != null &&
          currentFilter!.cattleId!.isNotEmpty &&
          notification.cattleId != currentFilter!.cattleId) {
        return false;
      }

      // Date range filter
      if (currentFilter!.fromDate != null &&
          notification.createdAt != null &&
          notification.createdAt!.isBefore(currentFilter!.fromDate!)) {
        return false;
      }

      if (currentFilter!.toDate != null &&
          notification.createdAt != null &&
          notification.createdAt!.isAfter(currentFilter!.toDate!)) {
        return false;
      }

      // Search text filter
      if (currentFilter!.searchText != null &&
          currentFilter!.searchText!.isNotEmpty) {
        final query = currentFilter!.searchText!.toLowerCase();
        final title = notification.title?.toLowerCase() ?? '';
        final message = notification.message?.toLowerCase() ?? '';

        if (!title.contains(query) && !message.contains(query)) {
          return false;
        }
      }

      return true;
    }).toList();

    // Sort notifications by creation date (newest first)
    final sortedFiltered = List<NotificationIsar>.from(filtered);
    sortedFiltered.sort((a, b) {
      final aDate = a.createdAt ?? DateTime.fromMillisecondsSinceEpoch(0);
      final bDate = b.createdAt ?? DateTime.fromMillisecondsSinceEpoch(0);
      return bDate.compareTo(aDate);
    });

    updateFilteredData(sortedFiltered);
  }

  /// Apply filters to notification data - triggers database-level filtering
  @override
  Future<void> applyFilters(NotificationFilterState filter) async {
    try {
      setState(ControllerState.loading);
      updateFilterState(filter);
      _applyCurrentFilters();
    } catch (e) {
      setError('Failed to apply filters: ${e.toString()}');
    }
  }

  /// Clear all filters
  @override
  Future<void> clearFilters() async {
    await applyFilters(NotificationFilterState.empty);
  }

  /// Check if filter has active filters
  @override
  bool hasFilters(NotificationFilterState filter) {
    return filter.hasActiveFilters;
  }

  //=== DATA OPERATIONS ===//

  /// Mark notification as read
  Future<void> markAsRead(String businessId) async {
    try {
      await _notificationRepository.markAsRead(businessId);
      // Stream will automatically update the UI
    } catch (e, stackTrace) {
      debugPrint('Error marking notification as read: $e\n$stackTrace');
      throw Exception('Failed to mark notification as read: ${e.toString()}');
    }
  }

  /// Delete notification
  Future<void> deleteNotification(String businessId) async {
    try {
      await _notificationRepository.deleteNotificationByBusinessId(businessId);
      // Stream will automatically update the UI
    } catch (e, stackTrace) {
      debugPrint('Error deleting notification: $e\n$stackTrace');
      throw Exception('Failed to delete notification: ${e.toString()}');
    }
  }

  //=== REFRESH ===//

  /// Refresh all data
  @override
  Future<void> refresh() async {
    try {
      // Force analytics recalculation on unfiltered data
      _updateAnalytics();
      _applyCurrentFilters();
      notifyListeners();
    } catch (e, stackTrace) {
      debugPrint('Error refreshing notifications data: $e\n$stackTrace');
      throw Exception('Failed to refresh notifications data: ${e.toString()}');
    }
  }

  //=== BACKWARD COMPATIBILITY METHODS ===//

  /// Apply filters using old NotificationFilter (backward compatibility)
  Future<void> applyFiltersLegacy(NotificationFilter filter) async {
    final newFilter = NotificationFilterState(
      status: filter.status,
      category: filter.category,
      cattleId: filter.cattleId,
      fromDate: filter.fromDate,
      toDate: filter.toDate,
      searchText: filter.searchQuery,
    );
    await applyFilters(newFilter);
  }
}
