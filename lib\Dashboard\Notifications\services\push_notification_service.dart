import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:http/http.dart' as http;
import 'package:logging/logging.dart';

import '../../../firebase_options.dart';
import 'firebase_config.dart';
import 'notification_settings_repository.dart';
import '../models/push_notification_request.dart';
import '../models/notification_priority.dart';

/// Service for handling Firebase Cloud Messaging operations
class PushNotificationService {
  final Logger _logger = Logger('PushNotificationService');
  final NotificationSettingsRepository _settingsRepository;
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
  
  /// Stream controller for notification taps
  final StreamController<RemoteMessage> _notificationTapController = StreamController<RemoteMessage>.broadcast();
  
  /// Stream of notification taps
  Stream<RemoteMessage> get onNotificationTap => _notificationTapController.stream;
  
  /// Firebase Messaging instance
  late final FirebaseMessaging _messaging;
  
  /// Whether the service is initialized
  bool _isInitialized = false;
  
  /// Constructor with NotificationSettingsRepository dependency
  PushNotificationService(this._settingsRepository);
  
  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) {
      _logger.info('PushNotificationService already initialized');
      return;
    }

    try {
      // Initialize Firebase if not already initialized
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );

      // Get Firebase Messaging instance
      _messaging = FirebaseMessaging.instance;
      
      // Configure notification channels for Android
      await _configureNotificationChannels();
      
      // Request permission
      await _requestPermission();
      
      // Configure message handling
      _configureMessageHandling();
      
      // Get and store token
      await _getAndStoreToken();
      
      _isInitialized = true;
      _logger.info('PushNotificationService initialized successfully');
    } catch (e) {
      _logger.severe('Error initializing PushNotificationService: $e');
      rethrow;
    }
  }
  
  /// Configure notification channels for Android
  Future<void> _configureNotificationChannels() async {
    if (Platform.isAndroid) {
      // Create default channel
      const AndroidNotificationChannel defaultChannel = AndroidNotificationChannel(
        FirebaseConfig.defaultChannelId,
        FirebaseConfig.defaultChannelName,
        description: FirebaseConfig.defaultChannelDescription,
        importance: Importance.defaultImportance,
      );
      
      // Create high priority channel
      const AndroidNotificationChannel highPriorityChannel = AndroidNotificationChannel(
        FirebaseConfig.highPriorityChannelId,
        FirebaseConfig.highPriorityChannelName,
        description: FirebaseConfig.highPriorityChannelDescription,
        importance: Importance.high,
      );
      
      // Create the channels
      await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(defaultChannel);
          
      await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(highPriorityChannel);
      
      _logger.info('Android notification channels configured');
    }
    
    // Initialize local notifications
    const AndroidInitializationSettings androidSettings = AndroidInitializationSettings('@drawable/ic_notification');
    const DarwinInitializationSettings iosSettings = DarwinInitializationSettings(
      requestAlertPermission: false,
      requestBadgePermission: false,
      requestSoundPermission: false,
    );
    
    const InitializationSettings initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );
    
    await _flutterLocalNotificationsPlugin.initialize(
      initSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) {
        // Handle notification tap
        _handleNotificationTap(response.payload);
      },
    );
    
    _logger.info('Local notifications initialized');
  }
  
  /// Request permission for push notifications
  Future<bool> _requestPermission() async {
    try {
      final settings = await _messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: true,
        provisional: false,
        sound: true,
      );
      
      final granted = settings.authorizationStatus == AuthorizationStatus.authorized ||
                     settings.authorizationStatus == AuthorizationStatus.provisional;
      
      _logger.info('Push notification permission ${granted ? 'granted' : 'denied'}');
      return granted;
    } catch (e) {
      _logger.warning('Error requesting push notification permission: $e');
      return false;
    }
  }
  
  /// Configure message handling for different app states
  void _configureMessageHandling() {
    // Handle messages when app is in foreground
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      _logger.info('Received foreground message: ${message.messageId}');
      handleForegroundMessage(message);
    });
    
    // Handle when app is opened from a notification
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      _logger.info('App opened from notification: ${message.messageId}');
      handleNotificationTap(message);
    });
    
    // Set up background message handler
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
    
    _logger.info('Message handling configured');
  }
  
  /// Get and store FCM token
  Future<String?> _getAndStoreToken() async {
    try {
      final token = await _messaging.getToken();
      
      if (token != null) {
        // Store token in settings
        await _settingsRepository.updateFCMToken(token);
        _logger.info('FCM token obtained and stored');
      } else {
        _logger.warning('Failed to get FCM token');
      }
      
      // Listen for token refreshes
      _messaging.onTokenRefresh.listen((String newToken) async {
        await _settingsRepository.updateFCMToken(newToken);
        _logger.info('FCM token refreshed and stored');
      });
      
      return token;
    } catch (e) {
      _logger.severe('Error getting FCM token: $e');
      return null;
    }
  }
  
  /// Get the current FCM token
  Future<String?> getToken() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      return await _messaging.getToken();
    } catch (e) {
      _logger.severe('Error getting FCM token: $e');
      return null;
    }
  }
  
  /// Refresh the FCM token
  Future<String?> refreshToken() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      await _messaging.deleteToken();
      final newToken = await _messaging.getToken();
      
      if (newToken != null) {
        await _settingsRepository.updateFCMToken(newToken);
        _logger.info('FCM token refreshed and stored');
      }
      
      return newToken;
    } catch (e) {
      _logger.severe('Error refreshing FCM token: $e');
      return null;
    }
  }
  
  /// Subscribe to a topic
  Future<void> subscribeToTopic(String topic) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      await _messaging.subscribeToTopic(topic);
      await _settingsRepository.subscribeToTopic(topic);
      _logger.info('Subscribed to topic: $topic');
    } catch (e) {
      _logger.severe('Error subscribing to topic: $e');
      rethrow;
    }
  }
  
  /// Unsubscribe from a topic
  Future<void> unsubscribeFromTopic(String topic) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      await _messaging.unsubscribeFromTopic(topic);
      await _settingsRepository.unsubscribeFromTopic(topic);
      _logger.info('Unsubscribed from topic: $topic');
    } catch (e) {
      _logger.severe('Error unsubscribing from topic: $e');
      rethrow;
    }
  }
  
  /// Send a push notification (alias for sendPushNotification)
  Future<bool> sendNotification(PushNotificationRequest request) async {
    return await sendPushNotification(request);
  }

  /// Send a push notification
  Future<bool> sendPushNotification(PushNotificationRequest request) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      // Convert request to FCM payload
      final payload = request.toFcmPayload();
      
      // Send using FCM HTTP v1 API
      final response = await http.post(
        Uri.parse('https://fcm.googleapis.com/v1/projects/${FirebaseConfig.projectId}/messages:send'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${FirebaseConfig.serverKey}',
        },
        body: jsonEncode(payload),
      );
      
      if (response.statusCode == 200) {
        _logger.info('Push notification sent successfully');
        return true;
      } else {
        _logger.warning('Failed to send push notification: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      _logger.severe('Error sending push notification: $e');
      return false;
    }
  }
  
  /// Handle foreground message
  void handleForegroundMessage(RemoteMessage message) {
    try {
      // Extract notification data
      final notification = message.notification;
      final data = message.data;
      
      if (notification != null) {
        // Determine priority
        final priorityStr = data['priority'] ?? 'medium';
        final priority = _getPriorityFromString(priorityStr);
        
        // Determine Android channel ID based on priority
        final channelId = priority == NotificationPriority.high || 
                         priority == NotificationPriority.critical
            ? FirebaseConfig.highPriorityChannelId
            : FirebaseConfig.defaultChannelId;
        
        // Show local notification
        _flutterLocalNotificationsPlugin.show(
          notification.hashCode,
          notification.title,
          notification.body,
          NotificationDetails(
            android: AndroidNotificationDetails(
              channelId,
              priority == NotificationPriority.high || priority == NotificationPriority.critical
                  ? FirebaseConfig.highPriorityChannelName
                  : FirebaseConfig.defaultChannelName,
              importance: priority == NotificationPriority.high || priority == NotificationPriority.critical
                  ? Importance.high
                  : Importance.defaultImportance,
              priority: priority == NotificationPriority.high || priority == NotificationPriority.critical
                  ? Priority.high
                  : Priority.defaultPriority,
              icon: '@drawable/ic_notification',
            ),
            iOS: const DarwinNotificationDetails(
              presentAlert: true,
              presentBadge: true,
              presentSound: true,
            ),
          ),
          payload: jsonEncode(data),
        );
        
        _logger.info('Displayed foreground notification: ${notification.title}');
      }
    } catch (e) {
      _logger.severe('Error handling foreground message: $e');
    }
  }
  
  /// Handle notification tap
  void handleNotificationTap(RemoteMessage message) {
    try {
      // Add message to stream for UI to handle navigation
      _notificationTapController.add(message);
      _logger.info('Notification tap handled: ${message.messageId}');
    } catch (e) {
      _logger.severe('Error handling notification tap: $e');
    }
  }
  
  /// Handle notification tap from payload
  void _handleNotificationTap(String? payload) {
    if (payload == null) return;
    
    try {
      final data = jsonDecode(payload) as Map<String, dynamic>;
      
      // Create a synthetic RemoteMessage
      final message = RemoteMessage(
        data: Map<String, dynamic>.from(data),
        messageId: data['messageId'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
        messageType: data['messageType'] ?? 'notification',
      );
      
      // Handle the tap
      handleNotificationTap(message);
    } catch (e) {
      _logger.severe('Error handling notification tap from payload: $e');
    }
  }
  
  /// Get NotificationPriority from string
  NotificationPriority _getPriorityFromString(String priorityStr) {
    switch (priorityStr.toLowerCase()) {
      case 'low':
        return NotificationPriority.low;
      case 'medium':
        return NotificationPriority.medium;
      case 'high':
        return NotificationPriority.high;
      case 'critical':
        return NotificationPriority.critical;
      default:
        return NotificationPriority.medium;
    }
  }
  
  /// Dispose resources
  void dispose() {
    _notificationTapController.close();
  }
}

/// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Initialize Firebase if not already initialized
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Log the message$1// $0 // Print statement commented out for production

  // No need to show a notification as FCM will automatically display it
}