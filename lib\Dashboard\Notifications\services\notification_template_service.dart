import 'package:logging/logging.dart';
import '../models/notification_isar.dart';
import '../models/notification_priority.dart';

/// Service for managing notification templates and content generation
class NotificationTemplateService {
  static final Logger _logger = Logger('NotificationTemplateService');
  
  /// Generate notification content based on type and data
  NotificationContent generateNotificationContent({
    required String type,
    required Map<String, dynamic> data,
    NotificationPriority priority = NotificationPriority.medium,
  }) {
    try {
      switch (type.toLowerCase()) {
        case 'health_alert':
          return _generateHealthAlertContent(data, priority);
        case 'breeding_reminder':
          return _generateBreedingReminderContent(data, priority);
        case 'milk_production':
          return _generateMilkProductionContent(data, priority);
        case 'weight_update':
          return _generateWeightUpdateContent(data, priority);
        case 'vaccination_due':
          return _generateVaccinationDueContent(data, priority);
        case 'system_alert':
          return _generateSystemAlertContent(data, priority);
        default:
          return _generateGenericContent(data, priority);
      }
    } catch (e, stackTrace) {
      _logger.severe('Error generating notification content: $e', e, stackTrace);
      return NotificationContent(
        title: 'Notification',
        message: 'A notification was generated for your cattle management system.',
        priority: priority,
      );
    }
  }
  
  /// Generate health alert notification content
  NotificationContent _generateHealthAlertContent(Map<String, dynamic> data, NotificationPriority priority) {
    final cattleName = data['cattleName'] ?? 'Unknown Cattle';
    final healthIssue = data['healthIssue'] ?? 'Health Issue';
    final severity = data['severity'] ?? 'moderate';
    
    String title;
    String message;
    
    switch (severity.toLowerCase()) {
      case 'critical':
        title = '🚨 Critical Health Alert';
        message = '$cattleName requires immediate veterinary attention for $healthIssue.';
        break;
      case 'high':
        title = '⚠️ Health Alert';
        message = '$cattleName shows signs of $healthIssue. Please monitor closely.';
        break;
      default:
        title = '📋 Health Update';
        message = '$cattleName has been noted with $healthIssue. Regular monitoring recommended.';
    }
    
    return NotificationContent(
      title: title,
      message: message,
      priority: priority,
    );
  }
  
  /// Generate breeding reminder notification content
  NotificationContent _generateBreedingReminderContent(Map<String, dynamic> data, NotificationPriority priority) {
    final cattleName = data['cattleName'] ?? 'Unknown Cattle';
    final reminderType = data['reminderType'] ?? 'breeding';
    final daysOverdue = data['daysOverdue'] ?? 0;
    
    String title;
    String message;
    
    if (daysOverdue > 0) {
      title = '⏰ Overdue Breeding Reminder';
      message = '$cattleName is $daysOverdue days overdue for $reminderType. Please take action.';
    } else {
      title = '🐄 Breeding Reminder';
      message = '$cattleName is due for $reminderType. Please schedule accordingly.';
    }
    
    return NotificationContent(
      title: title,
      message: message,
      priority: priority,
    );
  }
  
  /// Generate milk production notification content
  NotificationContent _generateMilkProductionContent(Map<String, dynamic> data, NotificationPriority priority) {
    final cattleName = data['cattleName'] ?? 'Unknown Cattle';
    final productionChange = data['productionChange'] ?? 0.0;
    final currentProduction = data['currentProduction'] ?? 0.0;
    
    String title;
    String message;
    
    if (productionChange > 0) {
      title = '📈 Milk Production Increase';
      message = '$cattleName\'s milk production increased by ${productionChange.toStringAsFixed(1)}L to ${currentProduction.toStringAsFixed(1)}L.';
    } else if (productionChange < 0) {
      title = '📉 Milk Production Decrease';
      message = '$cattleName\'s milk production decreased by ${(-productionChange).toStringAsFixed(1)}L to ${currentProduction.toStringAsFixed(1)}L.';
    } else {
      title = '🥛 Milk Production Update';
      message = '$cattleName produced ${currentProduction.toStringAsFixed(1)}L of milk today.';
    }
    
    return NotificationContent(
      title: title,
      message: message,
      priority: priority,
    );
  }
  
  /// Generate weight update notification content
  NotificationContent _generateWeightUpdateContent(Map<String, dynamic> data, NotificationPriority priority) {
    final cattleName = data['cattleName'] ?? 'Unknown Cattle';
    final weightChange = data['weightChange'] ?? 0.0;
    final currentWeight = data['currentWeight'] ?? 0.0;
    
    String title;
    String message;
    
    if (weightChange > 0) {
      title = '📊 Weight Gain Recorded';
      message = '$cattleName gained ${weightChange.toStringAsFixed(1)}kg, now weighing ${currentWeight.toStringAsFixed(1)}kg.';
    } else if (weightChange < 0) {
      title = '⚠️ Weight Loss Recorded';
      message = '$cattleName lost ${(-weightChange).toStringAsFixed(1)}kg, now weighing ${currentWeight.toStringAsFixed(1)}kg.';
    } else {
      title = '⚖️ Weight Update';
      message = '$cattleName\'s weight recorded as ${currentWeight.toStringAsFixed(1)}kg.';
    }
    
    return NotificationContent(
      title: title,
      message: message,
      priority: priority,
    );
  }
  
  /// Generate vaccination due notification content
  NotificationContent _generateVaccinationDueContent(Map<String, dynamic> data, NotificationPriority priority) {
    final cattleName = data['cattleName'] ?? 'Unknown Cattle';
    final vaccinationType = data['vaccinationType'] ?? 'vaccination';
    final dueDate = data['dueDate'] ?? DateTime.now();
    
    const title = '💉 Vaccination Due';
    final message = '$cattleName is due for $vaccinationType on ${_formatDate(dueDate)}.';
    
    return NotificationContent(
      title: title,
      message: message,
      priority: priority,
    );
  }
  
  /// Generate system alert notification content
  NotificationContent _generateSystemAlertContent(Map<String, dynamic> data, NotificationPriority priority) {
    final alertType = data['alertType'] ?? 'System Alert';
    final alertMessage = data['message'] ?? 'A system alert has been generated.';
    
    return NotificationContent(
      title: '🔔 $alertType',
      message: alertMessage,
      priority: priority,
    );
  }
  
  /// Generate generic notification content
  NotificationContent _generateGenericContent(Map<String, dynamic> data, NotificationPriority priority) {
    final title = data['title'] ?? 'Notification';
    final message = data['message'] ?? 'You have a new notification.';
    
    return NotificationContent(
      title: title,
      message: message,
      priority: priority,
    );
  }
  
  /// Format date for display
  String _formatDate(dynamic date) {
    if (date is DateTime) {
      return '${date.day}/${date.month}/${date.year}';
    } else if (date is String) {
      try {
        final parsedDate = DateTime.parse(date);
        return '${parsedDate.day}/${parsedDate.month}/${parsedDate.year}';
      } catch (e) {
        return date;
      }
    }
    return date.toString();
  }

  /// Create notification from request
  NotificationIsar createNotificationFromRequest(dynamic request) {
    try {
      // Handle different request types
      if (request is Map<String, dynamic>) {
        return NotificationIsar(
          businessId: request['businessId'] ?? _generateBusinessId(),
          title: request['title'] ?? 'Notification',
          message: request['message'] ?? 'You have a new notification',
          category: request['category'] ?? 'general',
          type: request['type'] ?? 'info',
          priority: _parsePriority(request['priority']),
          cattleId: request['cattleId'],
          relatedRecordId: request['relatedRecordId'],
          relatedRecordType: request['relatedRecordType'],
          eventId: request['eventId'],
          imageUrl: request['imageUrl'],
          actionUrl: request['actionUrl'],
          customData: request['customData']?.cast<String, String>(),
          scheduledFor: request['scheduledFor'] != null
              ? DateTime.tryParse(request['scheduledFor'].toString())
              : null,
          expiresAt: request['expiresAt'] != null
              ? DateTime.tryParse(request['expiresAt'].toString())
              : null,
          isRecurring: request['isRecurring'] ?? false,
          recurringPattern: request['recurringPattern'],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      } else {
        // Fallback for unknown request types
        return NotificationIsar(
          businessId: _generateBusinessId(),
          title: 'Notification',
          message: 'You have a new notification',
          category: 'general',
          type: 'info',
          priority: NotificationPriority.medium,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      }
    } catch (e, stackTrace) {
      _logger.severe('Error creating notification from request: $e', e, stackTrace);
      return NotificationIsar(
        businessId: _generateBusinessId(),
        title: 'Notification',
        message: 'You have a new notification',
        category: 'general',
        type: 'info',
        priority: NotificationPriority.medium,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }
  }

  /// Parse priority from dynamic value
  NotificationPriority _parsePriority(dynamic priority) {
    if (priority == null) return NotificationPriority.medium;

    if (priority is NotificationPriority) return priority;

    if (priority is String) {
      try {
        return NotificationPriority.values.firstWhere(
          (p) => p.name.toLowerCase() == priority.toLowerCase(),
          orElse: () => NotificationPriority.medium,
        );
      } catch (e) {
        return NotificationPriority.medium;
      }
    }

    if (priority is int) {
      try {
        return NotificationPriority.values[priority];
      } catch (e) {
        return NotificationPriority.medium;
      }
    }

    return NotificationPriority.medium;
  }

  /// Generate a unique business ID
  String _generateBusinessId() {
    return 'notif_${DateTime.now().millisecondsSinceEpoch}_${_generateRandomString(6)}';
  }

  /// Generate a random string
  String _generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    return List.generate(length, (index) => chars[(random + index) % chars.length]).join();
  }
}

/// Notification content data class
class NotificationContent {
  final String title;
  final String message;
  final NotificationPriority priority;
  final Map<String, dynamic>? additionalData;
  
  const NotificationContent({
    required this.title,
    required this.message,
    required this.priority,
    this.additionalData,
  });
}
