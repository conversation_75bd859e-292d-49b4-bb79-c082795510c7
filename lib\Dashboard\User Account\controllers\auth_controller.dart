import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';

import '../models/user_isar.dart';
import '../services/auth_service.dart';
import '../services/google_auth_service.dart';
import '../services/biometric_auth_service.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // For ControllerState enum

/// Authentication controller following the streamlined architecture
/// Manages user authentication state and operations
class AuthController extends ChangeNotifier {
  // Services
  final AuthService _authService = GetIt.instance<AuthService>();
  final GoogleAuthService _googleAuthService = GetIt.instance<GoogleAuthService>();
  final BiometricAuthService _biometricAuthService = GetIt.instance<BiometricAuthService>();

  // State
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;
  String? _successMessage;
  UserIsar? _currentUser;
  bool _isAuthenticated = false;
  bool _isInitialized = false;

  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;
  String? get successMessage => _successMessage;
  UserIsar? get currentUser => _currentUser;
  bool get isAuthenticated => _isAuthenticated;
  bool get isInitialized => _isInitialized;
  bool get isLoading => _state == ControllerState.loading;

  /// Initialize the auth controller
  Future<void> initialize() async {
    try {
      _setState(ControllerState.loading);
      
      // Check if user is already authenticated
      if (_authService.isAuthenticated) {
        _currentUser = _authService.currentUser;
        _isAuthenticated = true;
      }
      
      _isInitialized = true;
      _setState(ControllerState.loaded);
    } catch (e) {
      debugPrint('Error initializing auth controller: $e');
      _setError('Failed to initialize authentication');
    }
  }

  /// Login with email and password
  Future<bool> login({
    required String emailOrUsername,
    required String password,
    bool rememberMe = false,
  }) async {
    try {
      _setState(ControllerState.loading);

      final result = await _authService.login(
        emailOrUsername: emailOrUsername,
        password: password,
        rememberMe: rememberMe,
      );

      if (result.success) {
        _currentUser = _authService.currentUser;
        _isAuthenticated = true;
        _setSuccess(result.message);
        _setState(ControllerState.loaded);
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      debugPrint('Login error: $e');
      _setError('Login failed: ${e.toString()}');
      return false;
    }
  }

  /// Login with Google
  Future<bool> signInWithGoogle({bool rememberMe = false}) async {
    try {
      _setState(ControllerState.loading);

      final result = await _googleAuthService.signIn(rememberMe: rememberMe);
      if (result.success) {
        _currentUser = result.user;
        _isAuthenticated = true;
        _setSuccess(result.message);
        _setState(ControllerState.loaded);
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      debugPrint('Google login error: $e');
      _setError('Google login failed: ${e.toString()}');
      return false;
    }
  }

  /// Sign up with Google
  Future<bool> signUpWithGoogle() async {
    return await signInWithGoogle(); // Same process for Google
  }

  /// Login with biometrics
  Future<bool> authenticateWithBiometric() async {
    try {
      _setState(ControllerState.loading);

      final result = await _biometricAuthService.authenticateAndLogin();
      if (result.success) {
        // Get the current user after biometric authentication
        _currentUser = _authService.currentUser;
        _isAuthenticated = true;
        _setSuccess('Biometric login successful');
        _setState(ControllerState.loaded);
        return true;
      }

      _setError('Biometric authentication failed');
      return false;
    } catch (e) {
      debugPrint('Biometric login error: $e');
      _setError('Biometric login failed: ${e.toString()}');
      return false;
    }
  }

  /// Register new user
  Future<bool> register({
    required String email,
    required String username,
    required String password,
    required String firstName,
    required String lastName,
    String? phoneNumber,
  }) async {
    try {
      _setState(ControllerState.loading);

      final result = await _authService.register(
        email: email,
        username: username,
        password: password,
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber,
      );
      if (result.success) {
        _setSuccess(result.message);
        _setState(ControllerState.loaded);
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      debugPrint('Registration error: $e');
      _setError('Registration failed: ${e.toString()}');
      return false;
    }
  }

  /// Send password reset email
  Future<bool> requestPasswordReset(String email) async {
    try {
      _setState(ControllerState.loading);

      await _authService.requestPasswordReset(email);
      _setSuccess('Password reset email sent');
      _setState(ControllerState.loaded);
      return true;
    } catch (e) {
      debugPrint('Password reset error: $e');
      _setError('Failed to send password reset email: ${e.toString()}');
      return false;
    }
  }

  /// Change password
  Future<bool> changePassword(String currentPassword, String newPassword) async {
    try {
      _setState(ControllerState.loading);

      final result = await _authService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );

      if (result.success) {
        _setSuccess(result.message);
        _setState(ControllerState.loaded);
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      debugPrint('Change password error: $e');
      _setError('Failed to change password: ${e.toString()}');
      return false;
    }
  }

  /// Verify email
  Future<bool> verifyEmail({
    required String email,
    required String token,
  }) async {
    try {
      _setState(ControllerState.loading);

      final result = await _authService.verifyEmail(
        email: email,
        token: token,
      );
      
      if (result.success) {
        _setSuccess(result.message);
        _setState(ControllerState.loaded);
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      debugPrint('Email verification error: $e');
      _setError('Failed to verify email: ${e.toString()}');
      return false;
    }
  }

  /// Resend email verification
  Future<bool> resendEmailVerification({required String email}) async {
    try {
      _setState(ControllerState.loading);

      final result = await _authService.resendEmailVerification(email: email);
      
      if (result.success) {
        _setSuccess(result.message);
        _setState(ControllerState.loaded);
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      debugPrint('Resend verification error: $e');
      _setError('Failed to send verification email: ${e.toString()}');
      return false;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _authService.signOut();
      _currentUser = null;
      _isAuthenticated = false;
      _setSuccess('Logged out successfully');
    } catch (e) {
      debugPrint('Logout error: $e');
      _setError('Logout failed: ${e.toString()}');
    }
  }

  /// Check if biometric authentication is available
  Future<bool> isBiometricAvailable() async {
    return await _biometricAuthService.isBiometricAvailable();
  }

  /// Check if biometric authentication is enabled
  Future<bool> isBiometricEnabled() async {
    return await _biometricAuthService.isBiometricEnabled();
  }

  /// Get biometric description
  Future<String> getBiometricDescription() async {
    final type = await _biometricAuthService.getBiometricType();
    return _biometricAuthService.getBiometricTypeDescription(type != null ? [type] : []);
  }

  /// Enable biometric authentication
  Future<bool> enableBiometricAuth() async {
    if (_currentUser != null) {
      final result = await _biometricAuthService.enableBiometricAuth(_currentUser!);
      if (result.success) {
        _setSuccess('Biometric authentication enabled');
        return true;
      }
      _setError(result.message);
    }
    return false;
  }

  /// Disable biometric authentication
  Future<bool> disableBiometricAuth() async {
    try {
      await _biometricAuthService.disableBiometricAuth();
      _setSuccess('Biometric authentication disabled');
      return true;
    } catch (e) {
      debugPrint('Disable biometric error: $e');
      _setError('Failed to disable biometric authentication: ${e.toString()}');
      return false;
    }
  }

  /// Clear messages
  void clearMessages() {
    _errorMessage = null;
    _successMessage = null;
    notifyListeners();
  }

  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    _successMessage = null;
    notifyListeners();
  }

  void _setError(String error) {
    _state = ControllerState.error;
    _errorMessage = error;
    _successMessage = null;
    notifyListeners();
  }

  void _setSuccess(String message) {
    _successMessage = message;
    _errorMessage = null;
    notifyListeners();
  }
}
