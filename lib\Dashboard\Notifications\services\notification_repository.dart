import 'package:isar/isar.dart';
import '../../../services/database/isar_service.dart';
import '../../../core/base/base_repository.dart';
import '../models/notification_isar.dart';
import '../models/notification_filter.dart';
import '../models/notification_status.dart';

/// Pure reactive repository for Notifications module database operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
class NotificationRepository extends BaseRepository<NotificationIsar> {

  // Public constructor with explicit dependency injection
  NotificationRepository(IsarService isarService) : super(isarService);

  //=== REACTIVE NOTIFICATIONS STREAMS ===//

  /// Watches all notifications with reactive updates
  /// The controller is responsible for all filtering and sorting
  @override
  Stream<List<NotificationIsar>> watchAll() {
    return isar.notificationIsars.where().watch(fireImmediately: true);
  }

  /// Watches all notifications with reactive updates (alias for backward compatibility)
  Stream<List<NotificationIsar>> watchAllNotifications() {
    return watchAll();
  }

  //=== NOTIFICATIONS CRUD ===//

  /// Save (add or update) a notification using Isar's native upsert
  @override
  Future<void> save(NotificationIsar notification) async {
    await isar.writeTxn(() async {
      await isar.notificationIsars.put(notification);
    });
  }

  /// Save (add or update) a notification using Isar's native upsert (alias for backward compatibility)
  Future<void> saveNotification(NotificationIsar notification) async {
    await save(notification);
  }

  /// Delete a notification by its Isar ID
  @override
  Future<void> delete(int id) async {
    await isar.writeTxn(() async {
      await isar.notificationIsars.delete(id);
    });
  }

  /// Delete a notification by its Isar ID (alias for backward compatibility)
  Future<void> deleteNotification(int id) async {
    await delete(id);
  }

  /// Mark notification as read by business ID
  Future<void> markAsRead(String businessId) async {
    final notification = await isar.notificationIsars.getByBusinessId(businessId);
    if (notification != null) {
      notification.status = NotificationStatus.read;
      notification.readAt = DateTime.now();
      notification.updatedAt = DateTime.now();
      await save(notification);
    }
  }

  //=== QUERY METHODS FOR ANALYTICS ===//

  /// Get all notifications (for analytics and reports)
  /// Returns a Future<List> for one-time data fetching
  @override
  Future<List<NotificationIsar>> getAll() async {
    return await isar.notificationIsars.where().findAll();
  }

  /// Get all notifications (for analytics and reports) - alias for backward compatibility
  Future<List<NotificationIsar>> getAllNotifications() async {
    return await getAll();
  }

  /// Get a notification by its business ID
  Future<NotificationIsar?> getNotificationById(String businessId) async {
    return await isar.notificationIsars.filter().businessIdEqualTo(businessId).findFirst();
  }

  /// Get notifications with filtering (for controller use)
  Future<List<NotificationIsar>> getNotifications({NotificationFilter? filter}) async {
    if (filter == null) {
      return await getAllNotifications();
    }

    dynamic query = isar.notificationIsars.where();

    if (filter.status != null) {
      query = query.filter().statusEqualTo(filter.status!);
    }

    if (filter.category != null && filter.category!.isNotEmpty) {
      query = query.filter().categoryEqualTo(filter.category!);
    }

    if (filter.cattleId != null && filter.cattleId!.isNotEmpty) {
      query = query.filter().cattleIdEqualTo(filter.cattleId!);
    }

    if (filter.fromDate != null && filter.toDate != null) {
      query = query.filter().createdAtBetween(filter.fromDate!, filter.toDate!);
    }

    return await query.findAll();
  }

  /// Get notifications by status for analytics
  Future<List<NotificationIsar>> getNotificationsByStatus(NotificationStatus status) async {
    return await isar.notificationIsars
        .filter()
        .statusEqualTo(status)
        .findAll();
  }

  /// Get notifications by category for analytics
  Future<List<NotificationIsar>> getNotificationsByCategory(String category) async {
    return await isar.notificationIsars
        .filter()
        .categoryEqualTo(category)
        .findAll();
  }

  /// Get notifications by date range for analytics
  @override
  Future<List<NotificationIsar>> getByDateRange(DateTime startDate, DateTime endDate) async {
    return await isar.notificationIsars
        .filter()
        .createdAtBetween(startDate, endDate)
        .findAll();
  }

  /// Get notifications by date range for analytics (alias for backward compatibility)
  Future<List<NotificationIsar>> getNotificationsByDateRange(DateTime startDate, DateTime endDate) async {
    return await getByDateRange(startDate, endDate);
  }

  /// Get unread count for analytics
  Future<int> getUnreadCount() async {
    return await isar.notificationIsars
        .filter()
        .statusEqualTo(NotificationStatus.unread)
        .count();
  }

  /// Delete notification by business ID
  @override
  Future<void> deleteByBusinessId(String businessId) async {
    final notification = await isar.notificationIsars.getByBusinessId(businessId);
    if (notification != null) {
      await delete(notification.id);
    }
  }

  /// Delete notification by business ID (alias for backward compatibility)
  Future<void> deleteNotificationByBusinessId(String businessId) async {
    await deleteByBusinessId(businessId);
  }

  /// Archive notification by business ID
  Future<void> archiveNotification(String businessId) async {
    final notification = await getNotificationById(businessId);
    if (notification != null) {
      notification.status = NotificationStatus.archived;
      notification.updatedAt = DateTime.now();
      await save(notification);
    }
  }

  /// Create a new notification
  Future<void> createNotification(NotificationIsar notification) async {
    await save(notification);
  }

  /// Update an existing notification
  Future<void> updateNotification(NotificationIsar notification) async {
    notification.updatedAt = DateTime.now();
    await save(notification);
  }

  /// Get notification by business ID (alias for existing method)
  Future<NotificationIsar?> getNotificationByBusinessId(String businessId) async {
    return await getNotificationById(businessId);
  }

  /// Mark notification as actioned
  Future<void> markAsActioned(String businessId) async {
    final notification = await getNotificationById(businessId);
    if (notification != null) {
      notification.status = NotificationStatus.actioned;
      notification.updatedAt = DateTime.now();
      await save(notification);
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    final notifications = await isar.notificationIsars
        .filter()
        .statusEqualTo(NotificationStatus.unread)
        .findAll();

    await isar.writeTxn(() async {
      for (final notification in notifications) {
        notification.status = NotificationStatus.read;
        notification.readAt = DateTime.now();
        notification.updatedAt = DateTime.now();
        await isar.notificationIsars.put(notification);
      }
    });
  }

  /// Mark multiple notifications as read
  Future<void> markMultipleAsRead(List<String> businessIds) async {
    await isar.writeTxn(() async {
      for (final businessId in businessIds) {
        final notification = await isar.notificationIsars.filter().businessIdEqualTo(businessId).findFirst();
        if (notification != null) {
          notification.status = NotificationStatus.read;
          notification.readAt = DateTime.now();
          notification.updatedAt = DateTime.now();
          await isar.notificationIsars.put(notification);
        }
      }
    });
  }

  /// Delete multiple notifications
  Future<void> deleteMultiple(List<String> businessIds) async {
    await isar.writeTxn(() async {
      for (final businessId in businessIds) {
        final notification = await isar.notificationIsars.filter().businessIdEqualTo(businessId).findFirst();
        if (notification != null) {
          await isar.notificationIsars.delete(notification.id);
        }
      }
    });
  }

  /// Get notification counts by category
  Future<Map<String, int>> getNotificationCountsByCategory() async {
    final notifications = await getAllNotifications();
    final counts = <String, int>{};

    for (final notification in notifications) {
      final category = notification.category ?? 'Unknown';
      counts[category] = (counts[category] ?? 0) + 1;
    }

    return counts;
  }

  /// Clean up old notifications
  Future<void> cleanupOldNotifications(DateTime cutoffDate) async {
    final oldNotifications = await isar.notificationIsars
        .filter()
        .createdAtLessThan(cutoffDate)
        .findAll();

    await isar.writeTxn(() async {
      for (final notification in oldNotifications) {
        await isar.notificationIsars.delete(notification.id);
      }
    });
  }
}
