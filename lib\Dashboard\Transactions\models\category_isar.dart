import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import '../../../constants/app_icons.dart';

part 'category_isar.g.dart';

@collection
class CategoryIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String categoryId = '';

  @Index(type: IndexType.value)
  String name = '';

  String description = '';

  @Index(type: IndexType.hash)
  String type = '';

  // Store icon as codePoint
  int? iconCodePoint;

  // Optional fontFamily for the icon
  String iconFontFamily = 'MaterialIcons';

  DateTime createdAt = DateTime.now();

  DateTime updatedAt = DateTime.now();

  String? businessId;
  String? iconName;
  String? categoryType;
  String farmBusinessId = '';

  /// Static IDs for predefined categories to avoid ID regeneration on reinstall
  static const Map<String, String> staticIds = {
    // Income categories
    'milk sales': 'static_category_milk_sales_id',
    'animal sales': 'static_category_animal_sales_id',
    'other income': 'static_category_other_income_id',

    // Expense categories
    'feed': 'static_category_feed_id',
    'veterinary': 'static_category_veterinary_id',
    'supplies': 'static_category_supplies_id',
    'labor': 'static_category_labor_id',
    'other expenses': 'static_category_other_expenses_id',
  };

  CategoryIsar();

  // Getters for icon conversion
  @ignore
  IconData get icon {
    // First check if we have a valid stored icon
    if (iconCodePoint != null) {
      return IconData(iconCodePoint!, fontFamily: iconFontFamily);
    }

    // If no stored icon, try to get by category type
    final categoryIcon = type == 'Income'
        ? AppIcons.income
        : type == 'Expense'
            ? AppIcons.expense
            : AppIcons.getCategoryIcon(name);

    // If we have a type or name match, return it
    if (categoryIcon != AppIcons.defaultIcon) {
      return categoryIcon;
    }

    // Ultimate fallback
    return AppIcons.defaultIcon;
  }

  set icon(IconData value) {
    iconCodePoint = value.codePoint;
    iconFontFamily = value.fontFamily ?? 'MaterialIcons';
  }

  factory CategoryIsar.create({
    required String categoryId,
    required String name,
    required String description,
    required String type,
    required IconData icon,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    // For predefined categories, use static IDs
    final String normalizedName = name.toLowerCase();
    final String staticId = staticIds[normalizedName] ?? categoryId;

    final category = CategoryIsar()
      ..categoryId = staticId
      ..name = name
      ..description = description
      ..type = type
      ..iconCodePoint = icon.codePoint
      ..iconFontFamily = icon.fontFamily ?? 'MaterialIcons'
      ..createdAt = createdAt ?? DateTime.now()
      ..updatedAt = updatedAt ?? DateTime.now();

    return category;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': categoryId,
      'name': name,
      'description': description,
      'type': type,
      'icon': iconCodePoint,
      'iconFontFamily': iconFontFamily,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  Map<String, dynamic> toJson() => toMap();

  factory CategoryIsar.fromMap(Map<String, dynamic> map) {
    return CategoryIsar()
      ..categoryId = map['id'] as String
      ..name = map['name'] as String
      ..description = map['description'] as String
      ..type = map['type'] as String
      ..iconCodePoint = map['icon'] as int
      ..iconFontFamily = map['iconFontFamily'] as String? ?? 'MaterialIcons'
      ..createdAt = DateTime.parse(map['createdAt'] as String)
      ..updatedAt = DateTime.parse(map['updatedAt'] as String);
  }

  factory CategoryIsar.fromJson(Map<String, dynamic> json) =>
      CategoryIsar.fromMap(json);

  CategoryIsar copyWith({
    String? categoryId,
    String? name,
    String? description,
    String? type,
    IconData? icon,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    final category = CategoryIsar()
      ..id = id // Preserve the Isar internal ID
      ..categoryId = categoryId ?? this.categoryId
      ..name = name ?? this.name
      ..description = description ?? this.description
      ..type = type ?? this.type
      ..createdAt = createdAt ?? this.createdAt
      ..updatedAt = updatedAt ?? this.updatedAt;

    if (icon != null) {
      category.iconCodePoint = icon.codePoint;
      category.iconFontFamily = icon.fontFamily ?? 'MaterialIcons';
    } else {
      category.iconCodePoint = iconCodePoint;
      category.iconFontFamily = iconFontFamily;
    }

    return category;
  }

  // Use AppIcons instead of hardcoded icons
  static IconData get incomeIcon => AppIcons.income;
  static IconData get expenseIcon => AppIcons.expense;

  static List<CategoryIsar> getDefaultCategories() {
    final now = DateTime.now();
    return [
      CategoryIsar.create(
        categoryId: 'income',
        name: 'Income',
        description: 'Income category',
        type: 'Income',
        icon: incomeIcon,
        createdAt: now,
        updatedAt: now,
      ),
      CategoryIsar.create(
        categoryId: 'expense',
        name: 'Expense',
        description: 'Expense category',
        type: 'Expense',
        icon: expenseIcon,
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }

  @override
  String toString() {
    return 'CategoryIsar(id: $categoryId, name: $name, type: $type)';
  }
}
