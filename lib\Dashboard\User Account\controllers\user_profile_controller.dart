import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import '../models/user_isar.dart';
import '../models/user_settings_isar.dart';
import '../../Farm Setup/models/farm_isar.dart';
import '../services/user_profile_service.dart';
import '../services/auth_service.dart';
import '../services/error_handling_service.dart';

/// Controller for managing user profile state and operations
class UserProfileController extends ChangeNotifier {
  final Logger _logger = Logger('UserProfileController');
  final UserProfileService _profileService = UserProfileService();

  // Use GetIt to get the singleton AuthService instance
  AuthService get _authService => GetIt.instance<AuthService>();
  final ErrorHandlingService _errorHandler = ErrorHandlingService();

  // State variables
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;
  UserSettingsIsar? _userSettings;
  List<FarmIsar> _userFarms = [];
  int _profileCompletionPercentage = 0;

  // Getters
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String? get successMessage => _successMessage;
  UserIsar? get currentUser => _authService.currentUser;
  UserSettingsIsar? get userSettings => _userSettings;
  List<FarmIsar> get userFarms => _userFarms;
  int get profileCompletionPercentage => _profileCompletionPercentage;
  bool get isAuthenticated => _authService.isAuthenticated;

  /// Initialize the controller
  Future<void> initialize() async {
    if (!_authService.isAuthenticated) return;
    
    _setLoading(true);
    try {
      await Future.wait([
        _loadUserSettings(),
        _loadUserFarms(),
        _updateProfileCompletion(),
      ]);
    } catch (e) {
      _logger.severe('Error initializing profile controller: $e');
      _setError('Failed to load profile data');
    } finally {
      _setLoading(false);
    }
  }

  /// Update user profile
  Future<void> updateProfile({
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? farmName,
    String? farmLocation,
    String? farmDescription,
  }) async {
    _setLoading(true);
    _clearMessages();

    try {
      final result = await _profileService.updateProfile(
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber,
        farmName: farmName,
        farmLocation: farmLocation,
        farmDescription: farmDescription,
      );

      if (result.success) {
        _setSuccess(result.message);
        await _updateProfileCompletion();
        await _loadUserFarms(); // Reload farms in case farm info changed
      } else {
        _setError(result.message);
      }
    } catch (e) {
      _logger.severe('Error updating profile: $e');
      _errorHandler.logError('updateProfile', e);
      _setError(_errorHandler.handleProfileError(e));
    } finally {
      _setLoading(false);
    }
  }

  /// Update profile picture
  Future<void> updateProfilePicture({ImageSource? source}) async {
    _setLoading(true);
    _clearMessages();

    try {
      final result = await _profileService.updateProfilePicture(source: source);

      if (result.success) {
        _setSuccess(result.message);
        await _updateProfileCompletion();
      } else {
        _setError(result.message);
      }
    } catch (e) {
      _logger.severe('Error updating profile picture: $e');
      _setError('Failed to update profile picture');
    } finally {
      _setLoading(false);
    }
  }

  /// Remove profile picture
  Future<void> removeProfilePicture() async {
    _setLoading(true);
    _clearMessages();

    try {
      final result = await _profileService.removeProfilePicture();

      if (result.success) {
        _setSuccess(result.message);
        await _updateProfileCompletion();
      } else {
        _setError(result.message);
      }
    } catch (e) {
      _logger.severe('Error removing profile picture: $e');
      _setError('Failed to remove profile picture');
    } finally {
      _setLoading(false);
    }
  }

  /// Update user settings
  Future<void> updateSettings(UserSettingsIsar settings) async {
    _setLoading(true);
    _clearMessages();

    try {
      final result = await _profileService.updateSettings(settings);

      if (result.success) {
        _userSettings = settings;
        _setSuccess(result.message);
      } else {
        _setError(result.message);
      }
    } catch (e) {
      _logger.severe('Error updating settings: $e');
      _setError('Failed to update settings');
    } finally {
      _setLoading(false);
    }
  }

  /// Create a new farm
  Future<void> createFarm({
    required String farmName,
    required String location,
    String? description,
    String? contactPerson,
    String? contactPhone,
    String? contactEmail,
  }) async {
    _setLoading(true);
    _clearMessages();

    try {
      final result = await _profileService.createUserFarm(
        farmName: farmName,
        location: location,
        description: description,
        contactPerson: contactPerson,
        contactPhone: contactPhone,
        contactEmail: contactEmail,
      );

      if (result.success) {
        _setSuccess(result.message);
        await _loadUserFarms();
        await _updateProfileCompletion();
      } else {
        _setError(result.message);
      }
    } catch (e) {
      _logger.severe('Error creating farm: $e');
      _setError('Failed to create farm');
    } finally {
      _setLoading(false);
    }
  }

  /// Associate with existing farm
  Future<void> associateWithFarm(String farmBusinessId) async {
    _setLoading(true);
    _clearMessages();

    try {
      final result = await _profileService.associateWithFarm(farmBusinessId);

      if (result.success) {
        _setSuccess(result.message);
        await _loadUserFarms();
        await _updateProfileCompletion();
      } else {
        _setError(result.message);
      }
    } catch (e) {
      _logger.severe('Error associating with farm: $e');
      _setError('Failed to associate with farm');
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh all profile data
  Future<void> refresh() async {
    await initialize();
  }

  /// Clear messages
  void clearMessages() {
    _clearMessages();
    notifyListeners();
  }

  // Private helper methods

  Future<void> _loadUserSettings() async {
    try {
      _userSettings = await _profileService.getUserSettings();
    } catch (e) {
      _logger.warning('Error loading user settings: $e');
    }
  }

  Future<void> _loadUserFarms() async {
    try {
      _userFarms = await _profileService.getUserFarms();
    } catch (e) {
      _logger.warning('Error loading user farms: $e');
      _userFarms = [];
    }
  }

  Future<void> _updateProfileCompletion() async {
    try {
      _profileCompletionPercentage = _profileService.getProfileCompletionPercentage();
    } catch (e) {
      _logger.warning('Error updating profile completion: $e');
      _profileCompletionPercentage = 0;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _successMessage = null;
    notifyListeners();
  }

  void _setSuccess(String message) {
    _successMessage = message;
    _errorMessage = null;
    notifyListeners();
  }

  void _clearMessages() {
    _errorMessage = null;
    _successMessage = null;
  }


}
