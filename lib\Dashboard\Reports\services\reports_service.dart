import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../models/report_models.dart';
import 'reports_repository.dart';
import 'reports_analytics_service.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Milk Records/models/milk_record_isar.dart';
import '../../Transactions/models/transaction_isar.dart';
import 'performance_monitor.dart';

/// Reports Service
///
/// Business logic service for generating reports from all farm modules.
/// Uses ReportsRepository for data access and ReportsAnalyticsService for calculations.
/// Handles complex report generation logic, validation, and error handling.
class ReportsService {

  // Repository for data access - use GetIt dependency injection
  ReportsRepository get _reportsRepository => GetIt.instance<ReportsRepository>();

  /// Generate dashboard report with key metrics from all modules
  Future<ReportData> getDashboardReport(FilterState filter) async {
    return await PerformanceMonitor.measureLoadTime(
      'Dashboard Report Generation',
      () async {
        try {
          // Fetch all data from repository
          final cattle = await _reportsRepository.getAllCattle();
          final milkRecords = await _reportsRepository.getAllMilkRecords();
          final healthRecords = await _reportsRepository.getAllHealthRecords();
          final breedingRecords = await _reportsRepository.getAllBreedingRecords();
          final weightRecords = await _reportsRepository.getAllWeightRecords();
          final transactions = await _reportsRepository.getAllTransactions();
          final events = await _reportsRepository.getAllEvents();

          // Calculate analytics using the dedicated service
          final analytics = ReportsAnalyticsService.calculateDashboard(
            cattle: cattle,
            milkRecords: milkRecords,
            healthRecords: healthRecords,
            breedingRecords: breedingRecords,
            weightRecords: weightRecords,
            transactions: transactions,
            events: events,
            filter: filter,
          );

          final metrics = <String, ReportMetric>{};
          final chartData = <ChartPoint>[];
          final tableData = <Map<String, String>>[];
          final insights = <String>[];

          // Generate metrics from analytics
          metrics['total_cattle'] = ReportMetric.kpi(
            title: 'Total Cattle',
            value: analytics.totalCattle.toString(),
            icon: Icons.pets,
            color: Colors.brown,
            subtitle: '${analytics.activeCattle} active',
          );

          metrics['milk_production'] = ReportMetric.kpi(
            title: 'Milk Production',
            value: '${analytics.totalMilkProduction.toStringAsFixed(1)}L',
            icon: Icons.local_drink,
            color: Colors.lightBlue,
            subtitle: 'Total production',
          );

          metrics['health_records'] = ReportMetric.kpi(
            title: 'Health Records',
            value: analytics.totalHealthRecords.toString(),
            icon: Icons.health_and_safety,
            color: Colors.orange,
          );

          metrics['breeding_records'] = ReportMetric.kpi(
            title: 'Breeding Records',
            value: analytics.totalBreedingRecords.toString(),
            icon: Icons.favorite,
            color: Colors.pink,
          );

          metrics['net_profit'] = ReportMetric.kpi(
            title: 'Net Profit',
            value: '\$${analytics.netProfit.toStringAsFixed(2)}',
            icon: Icons.account_balance,
            color: analytics.netProfit >= 0 ? Colors.green : Colors.red,
          );
          // Generate basic insights
          if (analytics.totalCattle > 0) {
            insights.add('Farm has ${analytics.totalCattle} cattle with ${analytics.activeCattle} active');
          }
          if (analytics.totalMilkProduction > 0) {
            insights.add('Total milk production: ${analytics.totalMilkProduction.toStringAsFixed(1)}L');
          }
          if (analytics.netProfit != 0) {
            final profitText = analytics.netProfit >= 0 ? 'profit' : 'loss';
            insights.add('Net $profitText: \$${analytics.netProfit.abs().toStringAsFixed(2)}');
          }

          // Check performance and log recommendations
          PerformanceMonitor.checkMemoryUsage('Dashboard Report Generated');

          return ReportData(
            title: 'Farm Dashboard',
            subtitle: 'Overview of farm operations',
            generated: DateTime.now(),
            startDate: filter.startDate,
            endDate: filter.endDate,
            metrics: metrics,
            chartData: chartData,
            tableData: tableData,
            insights: insights,
            type: ReportType.dashboard,
          );
        } catch (e) {
          return ReportData.empty(ReportType.dashboard);
        }
      },
    );
  }

  /// Generate cattle report
  Future<ReportData> getCattleReport(FilterState filter) async {
    try {
      final cattle = await _reportsRepository.getAllCattle();
      final filteredCattle = _filterCattleByIds(cattle, filter.cattleIds);

      // Use the analytics service for report generation
      return ReportsAnalyticsService.calculateCattleReport(filteredCattle.cast<CattleIsar>(), filter);
    } catch (e) {
      return ReportData.empty(ReportType.cattle);
    }
  }

  /// Generate milk production report
  Future<ReportData> getMilkReport(FilterState filter) async {
    try {
      final milkRecords = await _reportsRepository.getAllMilkRecords();
      final filteredRecords = _filterByDate(milkRecords, filter);

      // Use the analytics service for report generation
      return ReportsAnalyticsService.calculateMilkReport(filteredRecords.cast<MilkRecordIsar>(), filter);
    } catch (e) {
      return ReportData.empty(ReportType.milk);
    }
  }

  /// Generate health report
  Future<ReportData> getHealthReport(FilterState filter) async {
    try {
      final healthRecords = await _reportsRepository.getAllHealthRecords();
      final filteredRecords = _filterByDate(healthRecords, filter);

      // For now, return a simple health report - can be enhanced later
      return ReportData(
        title: 'Health Records Report',
        subtitle: 'Overview of health records',
        generated: DateTime.now(),
        startDate: filter.startDate,
        endDate: filter.endDate,
        metrics: {
          'total_records': ReportMetric.kpi(
            title: 'Health Records',
            value: filteredRecords.length.toString(),
            icon: Icons.health_and_safety,
            color: Colors.red,
          ),
        },
        chartData: [],
        tableData: filteredRecords.map((record) => <String, String>{
          'Date': _formatDate(record.date),
          'Cattle': record.cattleTagNumber ?? 'N/A',
          'Condition': record.condition ?? 'N/A',
          'Treatment': record.treatment ?? 'N/A',
          'Status': record.status ?? 'N/A',
        }).toList(),
        insights: ['${filteredRecords.length} health records found'],
        type: ReportType.health,
      );
    } catch (e) {
      return ReportData.empty(ReportType.health);
    }
  }

  /// Generate financial report
  Future<ReportData> getFinancialReport(FilterState filter) async {
    try {
      final transactions = await _reportsRepository.getAllTransactions();
      final filteredTransactions = _filterByDate(transactions, filter);

      // Use the analytics service for report generation
      return ReportsAnalyticsService.calculateFinancialReport(filteredTransactions.cast<TransactionIsar>(), filter);
    } catch (e) {
      return ReportData.empty(ReportType.financial);
    }
  }

  // Helper methods

  List<dynamic> _filterByDate(List<dynamic> records, FilterState filter) {
    if (filter.startDate == null && filter.endDate == null) return records;
    
    return records.where((record) {
      final date = record.date as DateTime?;
      if (date == null) return false;
      
      if (filter.startDate != null && date.isBefore(filter.startDate!)) return false;
      if (filter.endDate != null && date.isAfter(filter.endDate!)) return false;
      
      return true;
    }).toList();
  }

  List<dynamic> _filterCattleByIds(List<dynamic> cattle, List<String>? ids) {
    if (ids == null || ids.isEmpty) return cattle;
    return cattle.where((animal) => ids.contains(animal.id)).toList();
  }





  String _formatDate(DateTime? date) {
    if (date == null) return 'N/A';
    return '${date.day}/${date.month}/${date.year}';
  }
}