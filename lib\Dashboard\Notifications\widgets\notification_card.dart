import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../models/notification_isar.dart';
import '../models/notification_status.dart';
import '../models/notification_priority.dart';
import '../../../constants/app_colors.dart';

/// Widget for displaying a notification card
class NotificationCard extends StatelessWidget {
  final NotificationIsar notification;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback? onTap;
  final VoidCallback? onSelect;
  final VoidCallback? onMarkAsRead;
  final VoidCallback? onArchive;
  final VoidCallback? onDelete;

  const NotificationCard({
    Key? key,
    required this.notification,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.onTap,
    this.onSelect,
    this.onMarkAsRead,
    this.onArchive,
    this.onDelete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      elevation: notification.status == NotificationStatus.unread ? 2 : 1,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: isSelected
                ? Border.all(color: Theme.of(context).primaryColor, width: 2)
                : null,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Selection checkbox or priority indicator
              if (isSelectionMode)
                Checkbox(
                  value: isSelected,
                  onChanged: (_) => onSelect?.call(),
                )
              else
                _buildPriorityIndicator(),
              
              const SizedBox(width: 12),
              
              // Notification content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title and timestamp
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Text(
                            notification.title ?? 'Notification',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: notification.status == NotificationStatus.unread
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _formatTimestamp(notification.createdAt),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // Message
                    if (notification.message != null && notification.message!.isNotEmpty)
                      Text(
                        notification.message!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: notification.status == NotificationStatus.unread
                              ? null
                              : AppColors.notificationStatusColors['read']!.withValues(alpha: 0.7),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    
                    const SizedBox(height: 8),
                    
                    // Category and status badges
                    Row(
                      children: [
                        Expanded(
                          child: Wrap(
                            spacing: 8,
                            runSpacing: 4,
                            children: [
                              if (notification.category != null)
                                _buildCategoryBadge(notification.category!),
                              _buildStatusBadge(notification.status),
                            ],
                          ),
                        ),

                        // Action buttons
                        if (!isSelectionMode) _buildActionButtons(context),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPriorityIndicator() {
    Color color;

    switch (notification.priority) {
      case NotificationPriority.critical:
        color = AppColors.notificationPriorityColors['critical']!;
        break;
      case NotificationPriority.high:
        color = AppColors.notificationPriorityColors['high']!;
        break;
      case NotificationPriority.medium:
        color = AppColors.notificationPriorityColors['medium']!;
        break;
      case NotificationPriority.low:
        color = AppColors.notificationPriorityColors['low']!;
        break;
    }

    return Container(
      width: 4,
      height: 40,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildCategoryBadge(String category) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getCategoryColor(category).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _getCategoryColor(category).withValues(alpha: 0.3)),
      ),
      child: Text(
        category.toUpperCase(),
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: _getCategoryColor(category),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(NotificationStatus status) {
    Color color;
    String text;

    switch (status) {
      case NotificationStatus.unread:
        color = AppColors.notificationStatusColors['unread']!;
        text = 'UNREAD';
        break;
      case NotificationStatus.read:
        color = AppColors.notificationStatusColors['read']!;
        text = 'READ';
        break;
      case NotificationStatus.archived:
        color = AppColors.notificationStatusColors['archived']!;
        text = 'ARCHIVED';
        break;
      case NotificationStatus.actioned:
        color = AppColors.notificationStatusColors['actioned']!;
        text = 'ACTIONED';
        break;
      case NotificationStatus.scheduled:
        color = Colors.blue;
        text = 'SCHEDULED';
        break;
      case NotificationStatus.pending:
        color = Colors.orange;
        text = 'PENDING';
        break;
      case NotificationStatus.delivered:
        color = Colors.green;
        text = 'DELIVERED';
        break;
      case NotificationStatus.failed:
        color = Colors.red;
        text = 'FAILED';
        break;
      case NotificationStatus.cancelled:
        color = Colors.grey;
        text = 'CANCELLED';
        break;
      case NotificationStatus.queued:
        color = Colors.purple;
        text = 'QUEUED';
        break;
      case NotificationStatus.expired:
        color = Colors.brown;
        text = 'EXPIRED';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 9,
          fontWeight: FontWeight.w600,
          color: color,
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_vert,
        size: 18,
        color: AppColors.notificationActionColors['filter'],
      ),
      constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
      padding: EdgeInsets.zero,
      itemBuilder: (context) => [
        if (notification.status == NotificationStatus.unread)
          PopupMenuItem<String>(
            value: 'markAsRead',
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.mark_email_read,
                  size: 16,
                  color: AppColors.notificationActionColors['markAsRead'],
                ),
                const SizedBox(width: 8),
                const Text('Mark as Read'),
              ],
            ),
          ),
        PopupMenuItem<String>(
          value: 'archive',
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.archive,
                size: 16,
                color: AppColors.notificationActionColors['archive'],
              ),
              const SizedBox(width: 8),
              const Text('Archive'),
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: 'delete',
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.delete,
                size: 16,
                color: AppColors.notificationActionColors['delete'],
              ),
              const SizedBox(width: 8),
              const Text('Delete'),
            ],
          ),
        ),
      ],
      onSelected: (value) {
        switch (value) {
          case 'markAsRead':
            onMarkAsRead?.call();
            break;
          case 'archive':
            onArchive?.call();
            break;
          case 'delete':
            onDelete?.call();
            break;
        }
      },
    );
  }

  Color _getCategoryColor(String category) {
    return AppColors.notificationCategoryColors[category.toLowerCase()] ??
           AppColors.notificationCategoryColors['system']!;
  }

  String _formatTimestamp(DateTime? timestamp) {
    if (timestamp == null) return '';
    
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return DateFormat('MMM d').format(timestamp);
    }
  }
}