import 'package:flutter/material.dart';
import '../widgets/dashboard_menu_item.dart';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';
import 'Farm Setup/services/farm_setup_repository.dart';
import 'Farm Setup/models/farm_isar.dart';
import 'Farm Setup/screens/farm_setup_screen.dart';

import 'widgets/app_drawer.dart';
import '../constants/app_bar.dart';
import '../constants/app_colors.dart';
import '../routes/app_routes.dart';
import 'User Account/screens/user_account_screen.dart';
import '../services/sync_service.dart';
import '../utils/message_utils.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final FarmSetupRepository _farmSetupRepository = GetIt.instance<FarmSetupRepository>();
  final SyncService _syncService = SyncService();
  FarmIsar? _currentFarm;
  bool _isLoading = true;
  bool _isSyncing = false;

  @override
  void initState() {
    super.initState();
    _loadCurrentFarm();
  }

  Future<void> _loadCurrentFarm() async {
    try {
      debugPrint('🏠 [DASHBOARD_DEBUG] _loadCurrentFarm() called at ${DateTime.now()}');
      setState(() => _isLoading = true);

      // First, let's check how many farms exist in total
      debugPrint('🏠 [DASHBOARD_DEBUG] Checking total farm count...');
      final allFarms = await _farmSetupRepository.getAllFarms();
      debugPrint('🏠 [DASHBOARD_DEBUG] Total farms in database: ${allFarms.length}');

      if (allFarms.isNotEmpty) {
        for (int i = 0; i < allFarms.length; i++) {
          final farm = allFarms[i];
          debugPrint('🏠 [DASHBOARD_DEBUG] Farm ${i + 1}: ${farm.name} (ID: ${farm.farmBusinessId})');
        }
      }

      debugPrint('🏠 [DASHBOARD_DEBUG] Calling farmSetupRepository.getCurrentFarm()...');
      final farm = await _farmSetupRepository.getCurrentFarm();

      if (farm != null) {
        debugPrint('✅ [DASHBOARD_DEBUG] Farm loaded successfully: ${farm.name} (ID: ${farm.farmBusinessId})');
      } else {
        debugPrint('⚠️ [DASHBOARD_DEBUG] No farm found in database');

        // If no farm found, let's try to create one as a fallback
        if (allFarms.isEmpty) {
          debugPrint('🏠 [DASHBOARD_DEBUG] No farms exist, creating emergency fallback farm...');
          try {
            final fallbackFarm = FarmIsar.create(
              id: const Uuid().v4(),
              name: 'My Farm',
              ownerName: 'Farm Owner',
              ownerContact: '+1234567890',
              ownerEmail: '<EMAIL>',
              farmType: FarmType.dairy,
              cattleCount: 0,
              capacity: 100,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            );

            final createdFarm = await _farmSetupRepository.addFarm(fallbackFarm);
            debugPrint('✅ [DASHBOARD_DEBUG] Emergency farm created: ${createdFarm.name}');

            setState(() {
              _currentFarm = createdFarm;
              _isLoading = false;
            });
            return;
          } catch (createError) {
            debugPrint('❌ [DASHBOARD_DEBUG] Failed to create emergency farm: $createError');
          }
        }
      }

      setState(() {
        _currentFarm = farm;
        _isLoading = false;
      });

      debugPrint('🏠 [DASHBOARD_DEBUG] Dashboard state updated - loading: false, farm: ${farm?.name ?? 'null'}');
    } catch (e) {
      debugPrint('❌ [DASHBOARD_DEBUG] Error loading farm: $e');
      debugPrint('❌ [DASHBOARD_DEBUG] Error type: ${e.runtimeType}');
      debugPrint('Error loading farm: $e');
      setState(() => _isLoading = false);
    }
  }

  /// Check network connectivity using SyncService
  Future<bool> _checkConnectivity() async {
    return await _syncService.checkConnectivity();
  }

  /// Comprehensive bidirectional sync functionality for all modules
  Future<void> _syncAllData() async {
    debugPrint('🔄 [SYNC_DEBUG] Sync Data button pressed at ${DateTime.now()}');

    if (_isSyncing) {
      debugPrint('⚠️ [SYNC_DEBUG] Sync already in progress, ignoring button press');
      return;
    }

    debugPrint('🔄 [SYNC_DEBUG] Starting sync process...');
    setState(() => _isSyncing = true);

    try {
      debugPrint('🔄 [SYNC_DEBUG] Current farm before sync: ${_currentFarm?.name ?? 'null'} (ID: ${_currentFarm?.farmBusinessId ?? 'null'})');

      // Check network connectivity first
      debugPrint('🔄 [SYNC_DEBUG] Checking network connectivity...');
      final hasConnection = await _checkConnectivity();
      debugPrint('🔄 [SYNC_DEBUG] Network connectivity check result: $hasConnection');

      if (!hasConnection) {
        debugPrint('⚠️ [SYNC_DEBUG] No internet connection, showing warning and returning');
        if (mounted) {
          MessageUtils.showWarning(
            context,
            'No internet connection. Please check your network and try again.',
          );
        }
        return;
      }

      if (mounted) {
        debugPrint('🔄 [SYNC_DEBUG] Showing sync start message to user');
        MessageUtils.showInfo(context, 'Starting bidirectional data synchronization...');
      }

      // Use the comprehensive sync service
      debugPrint('🔄 [SYNC_DEBUG] Calling syncService.syncAllData() with farm: ${_currentFarm?.farmBusinessId ?? 'null'}');
      final syncResult = await _syncService.syncAllData(_currentFarm);

      debugPrint('🔄 [SYNC_DEBUG] Sync service returned: success=${syncResult.success}, hasWarnings=${syncResult.hasWarnings}');
      debugPrint('🔄 [SYNC_DEBUG] Sync result message: $syncResult.summaryMessage');
      debugPrint('🔄 [SYNC_DEBUG] Sync result details: ${syncResult.results.join(', ')}');

      // Refresh local farm data after sync
      debugPrint('🔄 [SYNC_DEBUG] Refreshing local farm data after sync...');
      await _loadCurrentFarm();
      debugPrint('🔄 [SYNC_DEBUG] Farm data refreshed. Current farm: ${_currentFarm?.name ?? 'null'}');

      // Show result based on sync outcome
      if (mounted) {
        debugPrint('🔄 [SYNC_DEBUG] Showing sync result to user...');
        if (syncResult.success && !syncResult.hasWarnings) {
          debugPrint('✅ [SYNC_DEBUG] Showing success message');
          MessageUtils.showSuccess(
            context,
            syncResult.summaryMessage,
          );
        } else if (syncResult.success && syncResult.hasWarnings) {
          debugPrint('⚠️ [SYNC_DEBUG] Showing warning message');
          MessageUtils.showWarning(
            context,
            syncResult.summaryMessage,
          );
        } else {
          debugPrint('❌ [SYNC_DEBUG] Showing error message');
          MessageUtils.showError(
            context,
            syncResult.summaryMessage,
          );
        }
      } else {
        debugPrint('⚠️ [SYNC_DEBUG] Widget not mounted, skipping UI update');
      }

    } catch (e) {
      debugPrint('❌ [SYNC_DEBUG] Sync error caught: $e');
      debugPrint('❌ [SYNC_DEBUG] Error type: ${e.runtimeType}');
      debugPrint('❌ [SYNC_DEBUG] Stack trace: ${StackTrace.current}');

      debugPrint('Sync error: $e');
      if (mounted) {
        debugPrint('❌ [SYNC_DEBUG] Showing error message to user');
        MessageUtils.showError(
          context,
          'Sync failed: ${e.toString()}',
        );
      } else {
        debugPrint('⚠️ [SYNC_DEBUG] Widget not mounted, skipping error UI update');
      }
    } finally {
      debugPrint('🔄 [SYNC_DEBUG] Sync process completed, resetting sync state');
      if (mounted) {
        setState(() => _isSyncing = false);
        debugPrint('🔄 [SYNC_DEBUG] Sync state reset to false');
      } else {
        debugPrint('⚠️ [SYNC_DEBUG] Widget not mounted, sync state not reset');
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    final currentFarmName = _currentFarm?.name ?? 'My Cattle Manager';
    final screenSize = MediaQuery.of(context).size;
    int crossAxisCount = screenSize.width > 600 ? 3 : 2;

    return Scaffold(
      appBar: AppBarConfig.withDrawer(
        title: currentFarmName,
        context: context,
        actions: [
          AppBarConfig.notificationsButton(
            onPressed: () {
              // Handle notifications button tap
            },
          ),
        ],
      ),
      drawer: const AppDrawer(),
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Expanded(
                child: GridView.count(
                  padding: const EdgeInsets.all(12),
                  crossAxisCount: crossAxisCount,
                  mainAxisSpacing: 12.0,
                  crossAxisSpacing: 12.0,
                  childAspectRatio: 1.0,
                  children: [
                    DashboardMenuItem(
                      title: 'Cattle Records',
                      icon: Icons.pets,
                      color: Colors.blue,
                      onTap: () => Navigator.pushNamed(context, AppRoutes.cattle),
                    ),
                    DashboardMenuItem(
                      title: 'Milk Records',
                      icon: Icons.local_drink,
                      color: Colors.green,
                      onTap: () => Navigator.pushNamed(context, AppRoutes.milk),
                    ),
                    DashboardMenuItem(
                      title: 'Breeding',
                      icon: Icons.favorite,
                      color: Colors.pink,
                      onTap: () => Navigator.pushNamed(context, AppRoutes.breeding),
                    ),
                    DashboardMenuItem(
                      title: 'Health',
                      icon: Icons.medical_services,
                      color: Colors.teal,
                      onTap: () => Navigator.pushNamed(context, AppRoutes.health),
                    ),
                    DashboardMenuItem(
                      title: 'Weight',
                      icon: Icons.monitor_weight,
                      color: Colors.indigo,
                      onTap: () => Navigator.pushNamed(context, AppRoutes.weight),
                    ),
                    DashboardMenuItem(
                      title: 'Events',
                      icon: Icons.event,
                      color: Colors.orange,
                      onTap: () => Navigator.pushNamed(context, AppRoutes.events),
                    ),
                    DashboardMenuItem(
                      title: 'Transactions',
                      icon: Icons.account_balance_wallet,
                      color: Colors.purple,
                      onTap: () => Navigator.pushNamed(context, AppRoutes.transactions),
                    ),
                    DashboardMenuItem(
                      title: 'Reports',
                      icon: Icons.bar_chart,
                      color: Colors.red,
                      onTap: () => Navigator.pushNamed(context, AppRoutes.reports),
                    ),
                    DashboardMenuItem(
                      title: 'Farm Setup',
                      icon: Icons.settings,
                      color: Colors.deepPurple,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const FarmSetupScreen(),
                          ),
                        );
                      },
                    ),
                    DashboardMenuItem(
                      title: 'User Account',
                      icon: Icons.account_circle,
                      color: Colors.purple, // Changed from brown (forbidden) to purple
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const UserAccountScreen(),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                height: 48.0,
                child: ElevatedButton.icon(
                  icon: _isSyncing
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Icon(Icons.sync),
                  label: Text(_isSyncing ? 'Syncing...' : 'Sync Data'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  onPressed: _isSyncing ? null : _syncAllData,
                ),
              ),
                ],
              ),
            ),
      ),
    );
  }
}
