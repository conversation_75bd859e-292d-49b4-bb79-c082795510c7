// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAVnkmYHfyX1lyPHMA-DlMFnIftwCUy2Io',
    appId: '1:424170625220:web:j2fascoshm47c4taokpounu555jnnaim',
    messagingSenderId: '424170625220',
    projectId: 'cattle-manager-app',
    authDomain: 'cattle-manager-app.firebaseapp.com',
    storageBucket: 'cattle-manager-app.appspot.com',
    measurementId: 'G-MEASUREMENT123',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAVnkmYHfyX1lyPHMA-DlMFnIftwCUy2Io',
    appId: '1:424170625220:android:e2jegdnn3pg9r295ikule6gbf7vmn3ht',
    messagingSenderId: '424170625220',
    projectId: 'cattle-manager-app',
    storageBucket: 'cattle-manager-app.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'YOUR_IOS_API_KEY',
    appId: '1:YOUR_PROJECT_NUMBER:ios:YOUR_IOS_APP_ID',
    messagingSenderId: 'YOUR_SENDER_ID',
    projectId: 'cattle-manager-app',
    storageBucket: 'cattle-manager-app.appspot.com',
    iosBundleId: 'com.example.cattle_manager',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'YOUR_MACOS_API_KEY',
    appId: '1:YOUR_PROJECT_NUMBER:ios:YOUR_MACOS_APP_ID',
    messagingSenderId: 'YOUR_SENDER_ID',
    projectId: 'cattle-manager-app',
    storageBucket: 'cattle-manager-app.appspot.com',
    iosBundleId: 'com.example.cattle_manager',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'YOUR_WINDOWS_API_KEY',
    appId: '1:YOUR_PROJECT_NUMBER:web:YOUR_WINDOWS_APP_ID',
    messagingSenderId: 'YOUR_SENDER_ID',
    projectId: 'cattle-manager-app',
    authDomain: 'cattle-manager-app.firebaseapp.com',
    storageBucket: 'cattle-manager-app.appspot.com',
    measurementId: 'G-YOUR_MEASUREMENT_ID',
  );
}
