import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import '../models/milk_record_isar.dart';
import '../models/milk_sale_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../services/milk_repository.dart';
import '../services/milk_analytics_service.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // Import for ControllerState enum

/// Filter state object for decoupled filter management
/// Following the established FilterController pattern for consistency
class MilkFilterState {
  final String? searchQuery;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? cattleId;
  final String? session;

  const MilkFilterState({
    this.searchQuery,
    this.startDate,
    this.endDate,
    this.cattleId,
    this.session,
  });

  /// Check if any filters are active
  bool get hasActiveFilters =>
      (searchQuery?.isNotEmpty ?? false) ||
      startDate != null ||
      endDate != null ||
      (cattleId?.isNotEmpty ?? false) ||
      (session?.isNotEmpty ?? false);

  /// Create a copy with updated values
  MilkFilterState copyWith({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? cattleId,
    String? session,
  }) {
    return MilkFilterState(
      searchQuery: searchQuery ?? this.searchQuery,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      cattleId: cattleId ?? this.cattleId,
      session: session ?? this.session,
    );
  }

  /// Clear all filters
  static const MilkFilterState empty = MilkFilterState();
}

/// Reactive controller for the main milk records screen using Dual-Stream Pattern
/// Following the cattle module template: separate unfiltered and filtered streams
/// Unfiltered stream feeds analytics, filtered stream feeds UI
class MilkController extends ChangeNotifier {
  // Use lazy getters to avoid accessing GetIt services in constructor
  MilkRepository get _milkRepository => GetIt.instance<MilkRepository>();
  Isar get _isar => GetIt.instance<Isar>();

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;

  // Stream subscriptions for real-time updates - Separated for filtered/unfiltered data
  StreamSubscription<List<MilkRecordIsar>>? _unfilteredMilkRecordsSubscription;
  StreamSubscription<List<MilkSaleIsar>>? _unfilteredMilkSalesSubscription;
  StreamSubscription<List<CattleIsar>>? _cattleStreamSubscription;
  StreamSubscription<List<MilkRecordIsar>>? _filteredMilkRecordsSubscription;

  // Data - Critical separation: filtered data for UI, unfiltered for analytics
  List<MilkRecordIsar> _unfilteredMilkRecords = []; // Complete dataset for analytics calculations
  List<MilkSaleIsar> _unfilteredMilkSales = []; // Complete milk sales dataset
  List<CattleIsar> _unfilteredCattle = []; // Complete cattle dataset for analytics

  List<MilkRecordIsar> _filteredMilkRecords = []; // Filtered dataset for UI display
  bool _hasActiveFilters = false; // Track if filters are currently applied

  // Analytics result from the dedicated service - ALWAYS calculated on unfiltered data
  MilkAnalyticsResult _analyticsResult = MilkAnalyticsResult.empty;

  // Filter state management - decoupled from UI
  MilkFilterState _currentFilters = MilkFilterState.empty;

  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;

  /// Returns the filtered milk records for UI display
  /// This is what the MilkRecordsTab should show
  List<MilkRecordIsar> get milkRecords => List.unmodifiable(_filteredMilkRecords);

  /// Returns the complete unfiltered milk records for analytics
  /// This ensures analytics are always calculated on the full dataset
  List<MilkRecordIsar> get unfilteredMilkRecords => List.unmodifiable(_unfilteredMilkRecords);

  /// Returns the complete unfiltered milk sales for analytics
  List<MilkSaleIsar> get unfilteredMilkSales => List.unmodifiable(_unfilteredMilkSales);

  /// Returns the complete unfiltered cattle list for analytics
  List<CattleIsar> get unfilteredCattle => List.unmodifiable(_unfilteredCattle);

  // Main analytics object - single source of truth
  MilkAnalyticsResult get analytics => _analyticsResult;

  // Convenience getters for backward compatibility with UI
  int get totalMilkRecords => _analyticsResult.totalMilkRecords;
  int get totalMilkSales => _analyticsResult.totalMilkSales;
  double get totalMilkProduced => _analyticsResult.totalMilkProduced;
  double get totalMilkSold => _analyticsResult.totalMilkSold;
  double get totalMilkUsedForCalves => _analyticsResult.totalMilkUsedForCalves;
  double get totalMilkUsedForHome => _analyticsResult.totalMilkUsedForHome;
  double get totalMilkUsed => _analyticsResult.totalMilkUsed;
  double get averageDailyProduction => _analyticsResult.averageDailyProduction;
  double get averageProductionPerCattle => _analyticsResult.averageProductionPerCattle;
  double get totalRevenue => _analyticsResult.totalRevenue;
  double get averagePricePerLiter => _analyticsResult.averagePricePerLiter;
  Map<String, double> get sessionDistribution => _analyticsResult.sessionDistribution;
  Map<String, double> get cattleProductionDistribution => _analyticsResult.cattleProductionDistribution;
  Map<String, int> get productionFrequencyDistribution => _analyticsResult.productionFrequencyDistribution;
  Map<String, double> get buyerDistribution => _analyticsResult.buyerDistribution;
  Map<String, int> get paymentMethodDistribution => _analyticsResult.paymentMethodDistribution;
  double get productionConsistency => _analyticsResult.productionConsistency;
  double get salesEfficiency => _analyticsResult.salesEfficiency;
  String get topProducingCattle => _analyticsResult.topProducingCattle;
  double get topProducingCattleAmount => _analyticsResult.topProducingCattleAmount;
  String get bestBuyer => _analyticsResult.bestBuyer;
  double get bestBuyerVolume => _analyticsResult.bestBuyerVolume;
  double get productionTrend => _analyticsResult.productionTrend;
  double get priceTrend => _analyticsResult.priceTrend;
  int get daysWithProduction => _analyticsResult.daysWithProduction;
  int get daysWithSales => _analyticsResult.daysWithSales;
  bool get hasData => _unfilteredMilkRecords.isNotEmpty || _unfilteredMilkSales.isNotEmpty;

  // Additional getters for screens
  List<CattleIsar> get cattle => _unfilteredCattle;

  // Filter state access
  MilkFilterState get currentFilters => _currentFilters;

  // Constructor
  MilkController() {
    _initializeStreamListeners();
  }

  /// Initialize stream listeners for real-time updates using Isar's native watch
  /// Critical: Separate streams for unfiltered (analytics) and filtered (UI) data
  void _initializeStreamListeners() {
    // Primary stream: Unfiltered milk records for analytics calculations
    // This stream NEVER changes and always provides the complete dataset
    _unfilteredMilkRecordsSubscription = _isar.milkRecordIsars.where()
        .watch(fireImmediately: true)
        .listen((unfilteredMilkRecords) {
      _handleUnfilteredMilkRecordsUpdate(unfilteredMilkRecords);
    });

    // Primary stream: Unfiltered milk sales for analytics calculations
    // This stream NEVER changes and always provides the complete dataset
    _unfilteredMilkSalesSubscription = _isar.milkSaleIsars.where()
        .watch(fireImmediately: true)
        .listen((unfilteredMilkSales) {
      _handleUnfilteredMilkSalesUpdate(unfilteredMilkSales);
    });

    // Cattle stream for analytics
    _cattleStreamSubscription = _isar.cattleIsars.where()
        .watch(fireImmediately: true)
        .listen((cattle) {
      _unfilteredCattle = cattle;
      _calculateAnalytics();
    }, onError: (error) {
      debugPrint('❌ Cattle stream error: $error');
      // Don't set error state for cattle stream - milk can work without cattle data
    });

    // Initially, filtered data equals unfiltered data (no filters applied)
    _filteredMilkRecords = _unfilteredMilkRecords;
    _hasActiveFilters = false;
  }

  /// Handle unfiltered milk records data update
  void _handleUnfilteredMilkRecordsUpdate(List<MilkRecordIsar> unfilteredMilkRecords) {
    _unfilteredMilkRecords = unfilteredMilkRecords;
    _calculateAnalytics();

    // Update filtered data if no active filters
    if (!_hasActiveFilters) {
      _filteredMilkRecords = List.from(_unfilteredMilkRecords);
    }

    _setState(ControllerState.loaded);
    notifyListeners();
  }

  /// Handle unfiltered milk sales data update
  void _handleUnfilteredMilkSalesUpdate(List<MilkSaleIsar> unfilteredMilkSales) {
    _unfilteredMilkSales = unfilteredMilkSales;
    _calculateAnalytics();
    notifyListeners();
  }



  /// Calculate analytics using the dedicated service
  /// Critical: ALWAYS uses unfiltered data to ensure accurate analytics
  void _calculateAnalytics() {
    _analyticsResult = MilkAnalyticsService.calculate(
      _unfilteredMilkRecords, // Use unfiltered data for accurate analytics
      _unfilteredMilkSales,
      _unfilteredCattle,
    );
  }

  /// Apply filters using the FilterController pattern for decoupled filter management
  /// Critical Fix: This method now creates a separate filtered stream without affecting analytics
  void applyFilters(MilkFilterState filterState) async {
    // Update current filter state
    _currentFilters = filterState;

    // Cancel existing filtered subscription (but keep unfiltered stream for analytics)
    _filteredMilkRecordsSubscription?.cancel();

    // Check if filters are active
    _hasActiveFilters = filterState.hasActiveFilters;

    if (_hasActiveFilters) {
      // Build filtered query for UI display
      final filteredQuery = _buildFilteredQuery(filterState);

      // Create separate stream for filtered data
      _filteredMilkRecordsSubscription = filteredQuery.watch(fireImmediately: true)
          .listen((filteredList) {
        _handleFilteredDataUpdate(filteredList);
      });
    } else {
      // No filters: filtered data equals unfiltered data
      _filteredMilkRecords = List.from(_unfilteredMilkRecords);
      notifyListeners();
    }
  }

  /// Convenience method for backward compatibility and simple filter updates
  void updateFilters({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? cattleId,
    String? session,
  }) {
    final newFilters = _currentFilters.copyWith(
      searchQuery: searchQuery,
      startDate: startDate,
      endDate: endDate,
      cattleId: cattleId,
      session: session,
    );
    applyFilters(newFilters);
  }

  /// Clear all filters
  void clearFilters() {
    applyFilters(MilkFilterState.empty);
  }

  /// Update search query - convenience method for search functionality
  void updateSearchQuery(String query) {
    final newFilters = _currentFilters.copyWith(searchQuery: query);
    applyFilters(newFilters);
  }

  /// Handle filtered data updates - Used for UI display only
  /// Critical: This method NEVER affects analytics calculations
  void _handleFilteredDataUpdate(List<MilkRecordIsar> filteredList) async {
    try {
      // Update only the filtered dataset for UI display
      _filteredMilkRecords = filteredList;

      // Do NOT recalculate analytics here - they're handled by unfiltered stream
      // This ensures analytics always reflect the complete dataset

      notifyListeners();
    } catch (e) {
      debugPrint('Error handling filtered data update: $e');
      // Don't change state to error for filtered data issues
      notifyListeners();
    }
  }

  /// Build filtered Isar query based on FilterState object
  /// This method dynamically constructs database queries for optimal performance
  dynamic _buildFilteredQuery(MilkFilterState filterState) {
    // Start with base query
    dynamic currentQuery = _isar.milkRecordIsars.where();

    // Apply search filter at database level
    if (filterState.searchQuery?.isNotEmpty ?? false) {
      final searchTerm = filterState.searchQuery!.toLowerCase();
      currentQuery = currentQuery.filter().group((q) => q
          .notesContains(searchTerm, caseSensitive: false));
    }

    // Apply date range filters at database level
    if (filterState.startDate != null) {
      currentQuery = currentQuery.filter().dateGreaterThan(filterState.startDate!);
    }
    if (filterState.endDate != null) {
      // Add one day to make end date inclusive
      final inclusiveEndDate = filterState.endDate!.add(const Duration(days: 1));
      currentQuery = currentQuery.filter().dateLessThan(inclusiveEndDate);
    }

    // Apply cattle filter
    if (filterState.cattleId?.isNotEmpty ?? false) {
      currentQuery = currentQuery.filter().cattleBusinessIdEqualTo(filterState.cattleId!);
    }

    // Apply session filter
    if (filterState.session?.isNotEmpty ?? false) {
      currentQuery = currentQuery.filter().sessionEqualTo(filterState.session!);
    }

    // Apply sorting at database level for optimal performance
    return currentQuery.sortByDateDesc(); // Default sort by date (newest first)
  }

  // CRUD Methods - Single Source of Truth pattern
  // These methods only update the database, stream handles UI updates
  // Exceptions bubble up naturally to be handled by higher-level error handlers

  /// Add new milk record - only updates database, stream handles UI update
  Future<void> addMilkRecord(MilkRecordIsar record) async {
    await _milkRepository.saveMilkRecord(record);
    // Stream will handle the UI update automatically
  }

  /// Add new milk sale - only updates database, stream handles UI update
  Future<void> addMilkSale(MilkSaleIsar sale) async {
    await _milkRepository.saveMilkSale(sale);
    // Stream will handle the UI update automatically
  }

  /// Update milk record - only updates database, stream handles UI update
  Future<void> updateMilkRecord(MilkRecordIsar updatedRecord) async {
    await _milkRepository.saveMilkRecord(updatedRecord);
    // Stream will handle the UI update automatically
  }

  /// Update milk sale - only updates database, stream handles UI update
  Future<void> updateMilkSale(MilkSaleIsar updatedSale) async {
    await _milkRepository.saveMilkSale(updatedSale);
    // Stream will handle the UI update automatically
  }

  /// Delete milk record - only updates database, stream handles UI update
  Future<void> deleteMilkRecord(int recordId) async {
    await _milkRepository.deleteMilkRecord(recordId);
    // Stream will handle the UI update automatically
  }

  /// Delete milk sale - only updates database, stream handles UI update
  Future<void> deleteMilkSale(int saleId) async {
    await _milkRepository.deleteMilkSale(saleId);
    // Stream will handle the UI update automatically
  }

  // Helper methods
  CattleIsar? getCattle(String? cattleBusinessId) {
    if (cattleBusinessId == null) return null;
    try {
      return _unfilteredCattle.firstWhere(
        (cattle) => cattle.businessId == cattleBusinessId,
      );
    } catch (e) {
      return null;
    }
  }

  String getCattleName(String? cattleBusinessId) {
    final cattle = getCattle(cattleBusinessId);
    return cattle?.name ?? 'Unknown Cattle';
  }

  List<CattleIsar> get femaleCattleList {
    return _unfilteredCattle.where((c) => c.gender == CattleGender.female).toList();
  }

  /// Load data - for compatibility with analytics and insights tabs
  Future<void> loadData() async {
    try {
      _setState(ControllerState.loading);

      // Data loading is handled by stream listeners
      // This method exists for compatibility with tab refresh functionality
      // The actual data loading happens automatically via _initializeStreamListeners

      // Force analytics recalculation if we have data
      if (_unfilteredMilkRecords.isNotEmpty || _unfilteredMilkSales.isNotEmpty) {
        _calculateAnalytics();
      }

      _setState(ControllerState.loaded);
      notifyListeners();
    } catch (e, stackTrace) {
      debugPrint('Error loading milk data: $e\n$stackTrace');
      _setState(ControllerState.error);
      _errorMessage = 'Failed to load milk data: ${e.toString()}';
      notifyListeners();
    }
  }

  /// Refresh all data - compatible with analytics tab refresh functionality
  Future<void> refresh() async {
    try {
      // Force analytics recalculation on unfiltered data
      if (_unfilteredMilkRecords.isNotEmpty || _unfilteredMilkSales.isNotEmpty) {
        _calculateAnalytics();
      }

      // Notify listeners of the refresh
      notifyListeners();
    } catch (e, stackTrace) {
      debugPrint('Error refreshing milk data: $e\n$stackTrace');
      throw Exception('Failed to refresh milk data: ${e.toString()}');
    }
  }

  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  // _setError method removed - unused

  @override
  void dispose() {
    // Cancel all stream subscriptions to prevent memory leaks
    _unfilteredMilkRecordsSubscription?.cancel();
    _unfilteredMilkSalesSubscription?.cancel();
    _cattleStreamSubscription?.cancel();
    _filteredMilkRecordsSubscription?.cancel();
    super.dispose();
  }
}
