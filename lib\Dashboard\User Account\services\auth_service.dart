import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';

import '../models/user_isar.dart';
import '../models/user_session_isar.dart';
import '../models/user_settings_isar.dart';
import 'user_repository.dart';
import 'email_service.dart';


import 'package:flutter/foundation.dart';
/// Comprehensive authentication service with security features
class AuthService {
  // Remove singleton pattern - now managed by GetIt dependency injection

  final Logger _logger = Logger('AuthService');
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  // Use lazy getters to get services from dependency injection when needed
  UserRepository get _userRepository => GetIt.instance<UserRepository>();
  EmailService get _emailService => EmailService();
  
  UserIsar? _currentUser;
  UserSessionIsar? _currentSession;

  // Constants
  static const String _currentUserKey = 'current_user_id';
  static const String _currentSessionKey = 'current_session_token';
  static const int _saltLength = 32;
  static const int _tokenLength = 64;

  // Getters
  UserIsar? get currentUser => _currentUser;
  UserSessionIsar? get currentSession => _currentSession;
  bool get isAuthenticated => _currentUser != null && _currentSession?.isValid == true;

  /// Sign out the current user
  Future<void> signOut() async {
    if (_currentSession != null) {
      await _userRepository.deleteSession(_currentSession!.id.toString());
    }
    _currentUser = null;
    _currentSession = null;
    await _clearStoredSession();
    _logger.info('User signed out');
  }

  /// Initialize the auth service and restore session if available
  Future<bool> initialize() async {
    try {
      debugPrint('🔧 [AUTH_SERVICE] Initializing AuthService...');
      _logger.info('Initializing AuthService...');

      // Try to restore previous session
      debugPrint('🔍 [AUTH_SERVICE] Checking for stored session token...');
      final sessionToken = await _secureStorage.read(key: _currentSessionKey);
      if (sessionToken != null) {
        debugPrint('✅ [AUTH_SERVICE] Session token found, attempting to restore session');
        final session = await _userRepository.getSessionByToken(sessionToken);
        if (session != null && session.isValid) {
          debugPrint('✅ [AUTH_SERVICE] Valid session found, checking user...');
          final user = await _userRepository.getUserByBusinessId(session.userBusinessId!);
          if (user != null && user.canLogin) {
            _currentUser = user;
            _currentSession = session;
            session.updateLastAccessed();
            await _userRepository.updateSession(session);
            debugPrint('✅ [AUTH_SERVICE] Session restored successfully for user: ${user.email}');
            _logger.info('Session restored for user: ${user.email}');
            return true;
          } else {
            debugPrint('⚠️ [AUTH_SERVICE] User not found or cannot login');
          }
        } else {
          debugPrint('⚠️ [AUTH_SERVICE] Session not found or invalid');
        }
      } else {
        debugPrint('ℹ️ [AUTH_SERVICE] No stored session token found');
      }

      // Clear invalid session data
      debugPrint('🧹 [AUTH_SERVICE] Clearing invalid session data...');
      await _clearStoredSession();
      debugPrint('✅ [AUTH_SERVICE] AuthService initialization completed - no valid session');
      return false;
    } catch (e) {
      debugPrint('❌ [AUTH_SERVICE] Error initializing AuthService: $e');
      _logger.severe('Error initializing AuthService: $e');
      await _clearStoredSession();
      return false;
    }
  }

  /// Register a new user account
  Future<AuthResult> register({
    required String email,
    required String username,
    required String password,
    required String firstName,
    required String lastName,
    String? phoneNumber,
    String? farmName,
    String? farmLocation,
    String? farmDescription,
  }) async {
    try {
      _logger.info('Attempting to register user: $email');

      // Validate input
      final validation = _validateRegistrationInput(
        email: email,
        username: username,
        password: password,
        firstName: firstName,
        lastName: lastName,
      );
      if (!validation.success) {
        return validation;
      }

      // Check if user already exists
      final existingUser = await _userRepository.getUserByEmail(email);
      if (existingUser != null) {
        return AuthResult.failure('An account with this email already exists');
      }

      final existingUsername = await _userRepository.getUserByUsername(username);
      if (existingUsername != null) {
        return AuthResult.failure('This username is already taken');
      }

      // Hash password
      final salt = _generateSalt();
      final passwordHash = _hashPassword(password, salt);

      // Create user
      final user = UserIsar.create(
        email: email,
        username: username,
        password: password, // This will be ignored since we set hash/salt manually
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber,
        farmName: farmName,
        farmLocation: farmLocation,
        farmDescription: farmDescription,
      );

      user.passwordHash = passwordHash;
      user.passwordSalt = salt;
      
      // Generate email verification token
      final verificationToken = _generateToken();
      user.setEmailVerificationToken(verificationToken);

      // Save user
      await _userRepository.createUser(user);

      // Create default settings
      final settings = UserSettingsIsar.createDefault(userBusinessId: user.businessId!);
      await _userRepository.createUserSettings(settings);

      // Send email verification
      await _emailService.sendEmailVerification(
        email: email,
        verificationToken: verificationToken,
        userName: user.fullName,
      );

      // Send welcome email
      await _emailService.sendWelcomeEmail(
        email: email,
        userName: user.fullName,
      );

      _logger.info('User registered successfully: $email');
      return AuthResult.success('Registration successful. Please check your email for verification instructions.');

    } catch (e) {
      _logger.severe('Registration error: $e');
      return AuthResult.failure('Registration failed. Please try again.');
    }
  }

  /// Login with email/username and password
  Future<AuthResult> login({
    required String emailOrUsername,
    required String password,
    bool rememberMe = false,
    String? deviceInfo,
    String? ipAddress,
    String? userAgent,
    String? location,
  }) async {
    try {
      _logger.info('Attempting login for: $emailOrUsername');

      // Find user by email or username
      UserIsar? user = await _userRepository.getUserByEmail(emailOrUsername);
      user ??= await _userRepository.getUserByUsername(emailOrUsername);

      if (user == null) {
        return AuthResult.failure('Invalid email/username or password');
      }

      // Check if user can login
      if (!user.canLogin) {
        if (user.isLocked) {
          final lockTime = user.lockedUntil;
          if (lockTime != null && DateTime.now().isBefore(lockTime)) {
            final remainingMinutes = lockTime.difference(DateTime.now()).inMinutes;
            return AuthResult.failure('Account is locked. Try again in $remainingMinutes minutes.');
          } else {
            // Unlock account if lock time has passed
            user.resetFailedLogins();
            await _userRepository.updateUser(user);
          }
        } else if (!user.isActive) {
          return AuthResult.failure('Account is deactivated. Please contact support.');
        }
      }

      // Verify password
      if (!_verifyPassword(password, user.passwordHash!, user.passwordSalt!)) {
        user.incrementFailedLogin();
        await _userRepository.updateUser(user);

        if (user.isLocked) {
          // Send account locked notification
          await _emailService.sendAccountLockedNotification(
            email: user.email!,
            userName: user.fullName,
            lockedUntil: user.lockedUntil!,
          );
          return AuthResult.failure('Too many failed attempts. Account locked for 30 minutes.');
        }
        
        return AuthResult.failure('Invalid email/username or password');
      }

      // Reset failed login attempts on successful login
      user.resetFailedLogins();
      user.updateLastLogin();
      await _userRepository.updateUser(user);

      // Create session
      final sessionToken = _generateToken();
      final session = UserSessionIsar.create(
        userBusinessId: user.businessId!,
        sessionToken: sessionToken,
        deviceInfo: deviceInfo,
        ipAddress: ipAddress,
        userAgent: userAgent,
        location: location,
      );

      await _userRepository.createSession(session);

      // Store session securely only if rememberMe is true
      if (rememberMe) {
        await _secureStorage.write(key: _currentUserKey, value: user.businessId);
        await _secureStorage.write(key: _currentSessionKey, value: sessionToken);
      }

      _currentUser = user;
      _currentSession = session;

      // Update welcome screen preference to indicate user has completed authentication
      await _updateWelcomeScreenPreference();

      _logger.info('Login successful for user: ${user.email}');
      return AuthResult.success('Login successful');

    } catch (e) {
      _logger.severe('Login error: $e');
      return AuthResult.failure('Login failed. Please try again.');
    }
  }

  /// Login with Google user (no password verification needed)
  Future<AuthResult> loginWithGoogleUser(UserIsar user, {bool rememberMe = true}) async {
    try {
      _logger.info('Attempting Google login for: ${user.email}');

      // Check if user can login
      if (!user.canLogin) {
        if (user.isLocked) {
          return AuthResult.failure('Account is temporarily locked. Please try again later.');
        }
        if (!user.isActive) {
          return AuthResult.failure('Account is deactivated. Please contact support.');
        }
      }

      // Update user's last login
      user.updateLastLogin();
      await _userRepository.updateUser(user);

      // Create session
      final sessionToken = _generateToken();
      final session = UserSessionIsar.create(
        userBusinessId: user.businessId!,
        sessionToken: sessionToken,
        deviceInfo: 'Google Sign-In',
      );

      session.user.value = user;
      await _userRepository.createSession(session);

      // Store session token only if rememberMe is true
      if (rememberMe) {
        await _secureStorage.write(key: _currentUserKey, value: user.businessId);
        await _secureStorage.write(key: _currentSessionKey, value: sessionToken);
      }

      // Set current user and session
      _currentUser = user;
      _currentSession = session;

      // Update welcome screen preference to indicate user has completed authentication
      await _updateWelcomeScreenPreference();

      _logger.info('Google login successful for: ${user.email}');
      return AuthResult.success('Google login successful');
    } catch (e) {
      _logger.severe('Google login error: $e');
      return AuthResult.failure('Google login failed: ${e.toString()}');
    }
  }

  /// Logout current user
  Future<void> logout() async {
    try {
      if (_currentSession != null) {
        _currentSession!.invalidate();
        await _userRepository.updateSession(_currentSession!);
      }

      await _clearStoredSession();
      _currentUser = null;
      _currentSession = null;

      _logger.info('User logged out successfully');
    } catch (e) {
      _logger.severe('Logout error: $e');
    }
  }

  /// Generate password reset token
  Future<AuthResult> requestPasswordReset(String email) async {
    try {
      final user = await _userRepository.getUserByEmail(email);
      if (user == null) {
        // Don't reveal if email exists for security
        return AuthResult.success('If the email exists, a reset link has been sent.');
      }

      final resetToken = _generateToken();
      user.setPasswordResetToken(resetToken);
      await _userRepository.updateUser(user);

      // Send password reset email
      await _emailService.sendPasswordReset(
        email: email,
        resetToken: resetToken,
        userName: user.fullName,
      );

      _logger.info('Password reset requested for: $email');
      return AuthResult.success('Password reset link sent to your email.');

    } catch (e) {
      _logger.severe('Password reset request error: $e');
      return AuthResult.failure('Failed to process password reset request.');
    }
  }

  /// Reset password with token
  Future<AuthResult> resetPassword({
    required String email,
    required String token,
    required String newPassword,
  }) async {
    try {
      final user = await _userRepository.getUserByEmail(email);
      if (user == null || !user.needsPasswordReset || user.passwordResetToken != token) {
        return AuthResult.failure('Invalid or expired reset token.');
      }

      // Validate new password
      if (!_isValidPassword(newPassword)) {
        return AuthResult.failure('Password must be at least 8 characters with uppercase, lowercase, number, and special character.');
      }

      // Update password
      final salt = _generateSalt();
      final passwordHash = _hashPassword(newPassword, salt);
      
      user.passwordHash = passwordHash;
      user.passwordSalt = salt;
      user.clearPasswordResetToken();
      
      await _userRepository.updateUser(user);

      // Invalidate all sessions for security
      await _userRepository.invalidateAllUserSessions(user.businessId!);

      _logger.info('Password reset successful for: $email');
      return AuthResult.success('Password reset successful. Please login with your new password.');

    } catch (e) {
      _logger.severe('Password reset error: $e');
      return AuthResult.failure('Failed to reset password.');
    }
  }

  /// Verify email with token
  Future<AuthResult> verifyEmail({
    required String email,
    required String token,
  }) async {
    try {
      final user = await _userRepository.getUserByEmail(email);
      if (user == null || user.emailVerificationToken != token || !user.needsEmailVerification) {
        return AuthResult.failure('Invalid or expired verification token.');
      }

      user.verifyEmail();
      await _userRepository.updateUser(user);

      _logger.info('Email verified for: $email');
      return AuthResult.success('Email verified successfully.');

    } catch (e) {
      _logger.severe('Email verification error: $e');
      return AuthResult.failure('Failed to verify email.');
    }
  }

  /// Resend email verification
  Future<AuthResult> resendEmailVerification({required String email}) async {
    try {
      final user = await _userRepository.getUserByEmail(email);
      if (user == null) {
        return AuthResult.failure('User not found.');
      }

      if (user.isEmailVerified) {
        return AuthResult.success('Email is already verified.');
      }

      // Generate new verification token
      final verificationToken = _generateToken();
      user.setEmailVerificationToken(verificationToken);
      await _userRepository.updateUser(user);

      // Send new verification email
      final emailSent = await _emailService.sendEmailVerification(
        email: email,
        verificationToken: verificationToken,
        userName: user.fullName,
      );

      if (emailSent) {
        _logger.info('Email verification resent to: $email');
        return AuthResult.success('Verification email sent successfully.');
      } else {
        return AuthResult.failure('Failed to send verification email.');
      }

    } catch (e) {
      _logger.severe('Resend email verification error: $e');
      return AuthResult.failure('Failed to resend verification email.');
    }
  }

  /// Change password for authenticated user
  Future<AuthResult> changePassword({
    String? currentPassword,
    required String newPassword,
  }) async {
    try {
      if (!isAuthenticated) {
        return AuthResult.failure('User not authenticated.');
      }

      // Verify current password if provided
      if (currentPassword != null) {
        if (!_verifyPassword(currentPassword, _currentUser!.passwordHash!, _currentUser!.passwordSalt!)) {
          return AuthResult.failure('Current password is incorrect.');
        }
      }

      // Validate new password
      if (!_isValidPassword(newPassword)) {
        return AuthResult.failure('Password must be at least 8 characters with uppercase, lowercase, number, and special character.');
      }

      // Update password
      final salt = _generateSalt();
      final passwordHash = _hashPassword(newPassword, salt);
      
      _currentUser!.passwordHash = passwordHash;
      _currentUser!.passwordSalt = salt;
      _currentUser!.updatedAt = DateTime.now();

      await _userRepository.updateUser(_currentUser!);

      // Send password changed notification
      await _emailService.sendPasswordChangedNotification(
        email: _currentUser!.email!,
        userName: _currentUser!.fullName,
      );

      _logger.info('Password changed for user: ${_currentUser!.email}');
      return AuthResult.success('Password changed successfully.');

    } catch (e) {
      _logger.severe('Change password error: $e');
      return AuthResult.failure('Failed to change password.');
    }
  }

  // Private helper methods
  String _generateSalt() {
    final random = Random.secure();
    final saltBytes = List<int>.generate(_saltLength, (i) => random.nextInt(256));
    return base64.encode(saltBytes);
  }

  String _generateToken() {
    final random = Random.secure();
    final tokenBytes = List<int>.generate(_tokenLength, (i) => random.nextInt(256));
    return base64.encode(tokenBytes);
  }

  String _hashPassword(String password, String salt) {
    final saltBytes = base64.decode(salt);
    final passwordBytes = utf8.encode(password);
    final combined = [...passwordBytes, ...saltBytes];
    final digest = sha256.convert(combined);
    return digest.toString();
  }

  bool _verifyPassword(String password, String hash, String salt) {
    final computedHash = _hashPassword(password, salt);
    return computedHash == hash;
  }

  bool _isValidPassword(String password) {
    if (password.length < 8) return false;
    if (!password.contains(RegExp(r'[A-Z]'))) return false;
    if (!password.contains(RegExp(r'[a-z]'))) return false;
    if (!password.contains(RegExp(r'[0-9]'))) return false;
    if (!password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) return false;
    return true;
  }

  AuthResult _validateRegistrationInput({
    required String email,
    required String username,
    required String password,
    required String firstName,
    required String lastName,
  }) {
    if (email.isEmpty || !RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
      return AuthResult.failure('Please enter a valid email address.');
    }
    
    if (username.isEmpty || username.length < 3) {
      return AuthResult.failure('Username must be at least 3 characters long.');
    }
    
    if (!_isValidPassword(password)) {
      return AuthResult.failure('Password must be at least 8 characters with uppercase, lowercase, number, and special character.');
    }
    
    if (firstName.isEmpty || lastName.isEmpty) {
      return AuthResult.failure('First name and last name are required.');
    }
    
    return AuthResult.success('Validation passed');
  }

  Future<void> _clearStoredSession() async {
    await _secureStorage.delete(key: _currentUserKey);
    await _secureStorage.delete(key: _currentSessionKey);
  }

  /// Update user preferences to indicate welcome screen has been seen
  /// Note: This functionality was removed when SettingsRepository was deleted
  Future<void> _updateWelcomeScreenPreference() async {
    // No-op: SettingsRepository was removed during cleanup
    _logger.info('Skipping welcome screen preference update (SettingsRepository removed)');
  }
}

/// Result class for authentication operations
class AuthResult {
  final bool success;
  final String message;

  AuthResult._(this.success, this.message);

  factory AuthResult.success(String message) => AuthResult._(true, message);
  factory AuthResult.failure(String message) => AuthResult._(false, message);
}
