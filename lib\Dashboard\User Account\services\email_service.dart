import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:logging/logging.dart';
import 'package:mailer/mailer.dart';
import 'package:mailer/smtp_server.dart';
import '../../../config/email_config.dart';

// Email provider options
enum EmailProvider { smtp, emailjs, sendgrid, mock }

/// Service for handling email operations like verification and password reset
/// This implementation supports multiple email providers: SMTP, EmailJS, and SendGrid
class EmailService {
  static final EmailService _instance = EmailService._internal();
  factory EmailService() => _instance;
  EmailService._internal();

  final Logger _logger = Logger('EmailService');

  // Current provider - change this to switch between providers
  static const EmailProvider _currentProvider = EmailProvider.mock; // Change to smtp, emailjs, or sendgrid for production

  /// Send email verification email
  Future<bool> sendEmailVerification({
    required String email,
    required String verificationToken,
    required String userName,
  }) async {
    try {
      _logger.info('Sending email verification to: $email');

      final verificationLink = '${EmailConfig.baseUrl}${EmailConfig.verificationPath}?token=$verificationToken&email=${Uri.encodeComponent(email)}';

      final subject = EmailConfig.getVerificationEmailSubject();
      final htmlBody = EmailConfig.getVerificationEmailBody(
        userName: userName,
        verificationLink: verificationLink,
      );

      final success = await _sendEmail(
        to: email,
        subject: subject,
        htmlBody: htmlBody,
        textBody: _htmlToText(htmlBody),
      );

      if (success) {
        _logger.info('Email verification sent successfully to: $email');
      } else {
        _logger.severe('Failed to send email verification to: $email');
      }

      return success;
    } catch (e) {
      _logger.severe('Failed to send email verification: $e');
      return false;
    }
  }

  /// Send password reset email
  Future<bool> sendPasswordReset({
    required String email,
    required String resetToken,
    required String userName,
  }) async {
    try {
      _logger.info('Sending password reset email to: $email');

      final resetLink = '${EmailConfig.baseUrl}${EmailConfig.passwordResetPath}?token=$resetToken&email=${Uri.encodeComponent(email)}';

      final subject = EmailConfig.getPasswordResetEmailSubject();
      final htmlBody = EmailConfig.getPasswordResetEmailBody(
        userName: userName,
        resetLink: resetLink,
      );

      final success = await _sendEmail(
        to: email,
        subject: subject,
        htmlBody: htmlBody,
        textBody: _htmlToText(htmlBody),
      );

      if (success) {
        _logger.info('Password reset email sent successfully to: $email');
      } else {
        _logger.severe('Failed to send password reset email to: $email');
      }

      return success;
    } catch (e) {
      _logger.severe('Failed to send password reset email: $e');
      return false;
    }
  }

  /// Send welcome email after successful registration
  Future<bool> sendWelcomeEmail({
    required String email,
    required String userName,
  }) async {
    try {
      _logger.info('Sending welcome email to: $email');

      final subject = EmailConfig.getWelcomeEmailSubject();
      final htmlBody = EmailConfig.getWelcomeEmailBody(userName: userName);

      final success = await _sendEmail(
        to: email,
        subject: subject,
        htmlBody: htmlBody,
        textBody: _htmlToText(htmlBody),
      );

      if (success) {
        _logger.info('Welcome email sent successfully to: $email');
      } else {
        _logger.severe('Failed to send welcome email to: $email');
      }

      return success;
    } catch (e) {
      _logger.severe('Failed to send welcome email: $e');
      return false;
    }
  }

  /// Send account locked notification
  Future<bool> sendAccountLockedNotification({
    required String email,
    required String userName,
    required DateTime lockedUntil,
  }) async {
    try {
      _logger.info('Sending account locked notification to: $email');
      
      final unlockTime = lockedUntil.toString();
      
      final emailContent = '''
      Dear $userName,
      
      Your Cattle Manager App account has been temporarily locked due to multiple failed login attempts.
      
      Your account will be automatically unlocked at: $unlockTime
      
      If this wasn't you, please contact our support team immediately.
      
      For security reasons, we recommend:
      - Using a strong, unique password
      - Enabling two-factor authentication when available
      - Not sharing your login credentials
      
      Best regards,
      Cattle Manager Team
      ''';
      
      // In production, you would use an email service provider here
      _logger.info('Account locked notification sent successfully');
      _logger.info('Email content: $emailContent');
      
      return true;
    } catch (e) {
      _logger.severe('Failed to send account locked notification: $e');
      return false;
    }
  }

  /// Send password changed notification
  Future<bool> sendPasswordChangedNotification({
    required String email,
    required String userName,
  }) async {
    try {
      _logger.info('Sending password changed notification to: $email');
      
      final emailContent = '''
      Dear $userName,
      
      Your Cattle Manager App password has been successfully changed.
      
      If you made this change, no further action is required.
      
      If you didn't change your password, please contact our support team immediately
      and consider the following steps:
      - Change your password immediately
      - Review your account activity
      - Enable additional security measures
      
      Best regards,
      Cattle Manager Team
      ''';
      
      // In production, you would use an email service provider here
      _logger.info('Password changed notification sent successfully');
      _logger.info('Email content: $emailContent');
      
      return true;
    } catch (e) {
      _logger.severe('Failed to send password changed notification: $e');
      return false;
    }
  }

  /// Core email sending method that routes to the appropriate provider
  Future<bool> _sendEmail({
    required String to,
    required String subject,
    required String htmlBody,
    required String textBody,
  }) async {
    switch (_currentProvider) {
      case EmailProvider.smtp:
        return await _sendEmailViaSMTP(to, subject, htmlBody, textBody);
      case EmailProvider.emailjs:
        return await _sendEmailViaEmailJS(to, subject, htmlBody);
      case EmailProvider.sendgrid:
        return await _sendEmailViaSendGrid(to, subject, htmlBody, textBody);
      case EmailProvider.mock:
        return _sendEmailViaMock(to, subject, htmlBody, textBody);
    }
  }

  /// Send email via SMTP (Gmail, Outlook, etc.)
  Future<bool> _sendEmailViaSMTP(String to, String subject, String htmlBody, String textBody) async {
    try {
      final smtpServer = SmtpServer(
        EmailConfig.smtpHost,
        port: EmailConfig.smtpPort,
        username: EmailConfig.smtpUsername,
        password: EmailConfig.smtpPassword,
        ssl: false,
        allowInsecure: false,
      );

      final message = Message()
        ..from = const Address(EmailConfig.noReplyEmail, EmailConfig.appName)
        ..recipients.add(to)
        ..subject = subject
        ..text = textBody
        ..html = htmlBody;

      final sendReport = await send(message, smtpServer);
      _logger.info('SMTP Email sent: ${sendReport.toString()}');
      return true;
    } catch (e) {
      _logger.severe('SMTP Email sending failed: $e');
      return false;
    }
  }

  /// Send email via EmailJS (client-side email service)
  Future<bool> _sendEmailViaEmailJS(String to, String subject, String htmlBody) async {
    try {
      final url = Uri.parse('https://api.emailjs.com/api/v1.0/email/send');
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'service_id': EmailConfig.emailJsServiceId,
          'template_id': EmailConfig.emailJsTemplateId,
          'user_id': EmailConfig.emailJsUserId,
          'template_params': {
            'to_email': to,
            'subject': subject,
            'html_content': htmlBody,
            'from_name': EmailConfig.appName,
          },
        }),
      );

      if (response.statusCode == 200) {
        _logger.info('EmailJS email sent successfully');
        return true;
      } else {
        _logger.severe('EmailJS failed: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      _logger.severe('EmailJS sending failed: $e');
      return false;
    }
  }

  /// Send email via SendGrid API
  Future<bool> _sendEmailViaSendGrid(String to, String subject, String htmlBody, String textBody) async {
    try {
      final url = Uri.parse('https://api.sendgrid.com/v3/mail/send');
      final response = await http.post(
        url,
        headers: {
          'Authorization': 'Bearer ${EmailConfig.sendGridApiKey}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'personalizations': [
            {
              'to': [{'email': to}],
              'subject': subject,
            }
          ],
          'from': {
            'email': EmailConfig.sendGridFromEmail,
            'name': EmailConfig.sendGridFromName,
          },
          'content': [
            {'type': 'text/plain', 'value': textBody},
            {'type': 'text/html', 'value': htmlBody},
          ],
        }),
      );

      if (response.statusCode == 202) {
        _logger.info('SendGrid email sent successfully');
        return true;
      } else {
        _logger.severe('SendGrid failed: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      _logger.severe('SendGrid sending failed: $e');
      return false;
    }
  }

  /// Mock email sending for development/testing
  bool _sendEmailViaMock(String to, String subject, String htmlBody, String textBody) {
    _logger.info('📧 MOCK EMAIL SENT 📧');
    _logger.info('To: $to');
    _logger.info('Subject: $subject');
    _logger.info('HTML Body: $htmlBody');
    _logger.info('Text Body: $textBody');
    _logger.info('📧 END MOCK EMAIL 📧');
    return true;
  }

  /// Convert HTML to plain text (basic implementation)
  String _htmlToText(String html) {
    return html
        .replaceAll(RegExp(r'<[^>]*>'), '') // Remove HTML tags
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&amp;', '&')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&quot;', '"')
        .trim();
  }

  /// Validate email format
  bool isValidEmail(String email) {
    // Basic format check
    if (!RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email)) {
      return false;
    }

    // Additional checks for edge cases
    if (email.contains('..')) return false; // Consecutive dots
    if (email.startsWith('.') || email.endsWith('.')) return false; // Leading/trailing dots
    if (email.contains('@.') || email.contains('.@')) return false; // Dot adjacent to @

    return true;
  }

  /// Generate email verification link
  String generateVerificationLink(String email, String token) {
    return '${EmailConfig.baseUrl}${EmailConfig.verificationPath}?token=$token&email=${Uri.encodeComponent(email)}';
  }

  /// Generate password reset link
  String generatePasswordResetLink(String email, String token) {
    return '${EmailConfig.baseUrl}${EmailConfig.passwordResetPath}?token=$token&email=${Uri.encodeComponent(email)}';
  }
}
